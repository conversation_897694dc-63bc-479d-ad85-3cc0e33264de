#! python
"""
Dag implementing for use CASE workflowUteGenericmarker
"""
import os
import sys

from airflow.utils import trigger_rule

from packages.arti_dags_common_package_ARTI_DAG_VERSION.profiling.dag_helper import DagHelper
from packages.arti_dags_common_package_ARTI_DAG_VERSION.profiling.params_action_helper import get_and_evaluate_dict
from packages.arti_dags_common_package_ARTI_DAG_VERSION.utils.bigquery_helpers import gcp_iam_access_token
from datetime import datetime


def import_dag_config():
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
    sys.path.append(SCRIPT_DIR)
    from dag_config import get_dag_config
    return get_dag_config()


my_dag_config = import_dag_config()
dag_helper = DagHelper(
    __file__,
    my_dag_config,
    dag_suffix='',
    schedule_interval='0 5 * * *',
    schedule_periodicity=DagHelper.DAY,
    schedule_when='05:00'
)

dag_helper.add_default_conf(
    "oneWeekAgoDate", "{{ (data_interval_end - macros.timedelta(days=7)).strftime('%Y%m%d') }}"
)
# On précise les fichiers jar à utiliser
jar_list = my_dag_config.get_extra_dag_config('EXTERNAL_DEPENDENDENCIES_VERSIONS')
jar_list.append(f"{my_dag_config.workflow_name}-{my_dag_config.version}.jar")
dag_helper.set_jar_list(jar_list)


def func_counterscheck(xcom_key, counter_value, threshold, action_if_ok, action_if_error, **kwargs):
    args_dict = {
        'counter_value': counter_value,
        'threshold': threshold,
        'action_if_ok': action_if_ok,
        'action_if_error': action_if_error
    }
    args_def = get_and_evaluate_dict(
        kwargs["ti"], args_dict, xcom_key, {}, False
    )

    if (int(args_def['counter_value']) > int(args_def['threshold'])):
        return args_def['action_if_ok']
    return args_def['action_if_error']

def check_if_tuesday(xcom_key,action_if_ok,action_if_error,**kwargs):
    args_dict = {
        'process_date': kwargs['process_date'],
        'action_if_ok':action_if_ok,
        'action_if_error':action_if_error
    }
    args_def = get_and_evaluate_dict(
        kwargs["ti"], args_dict, xcom_key, {}, False
    )
    process_date_str = args_def['process_date']
    process_date = datetime.strptime(process_date_str, '%Y%m%d')
    if process_date.strftime('%A') == 'Tuesday':
        return args_def['action_if_ok']
    return args_def['action_if_error']


with dag_helper.create_dag(
    doc_md="""
        Calcul des profils génétiques et des profiles types pour Meta4U/ScoreTV.
        Calcul des verticales (score des thématiques).
        Les profils génétiques sont envoyés par ce workflow.
        Les profils types et les verticales sont mis à disposition du Scoring pour envoi avec les autres marqueurs.
    """
) as dag:

    job_pre_init = dag_helper.pre_init_op()
    job_input_files_check = dag_helper.input_files_check_op()

    create_dataproc_cluster = dag_helper.create_dataproc_cluster_op()


    job_init = dag_helper.submit_dataproc_job_op(
        task_id='py_init',
        doc="Initialisation de configurations calculées pour ce traitement",
        job_name='init',
        job_type='hadoop_job',
        capture=True
    )

    job_filterAndTimeslot = dag_helper.submit_dataproc_job_op(
        task_id="py_filterAndTimeslot",
        doc="Regroupement des zaps par programmes et affectation à un timeslot",
        job_name="filterAndTimeslot",
        job_type='hadoop_job'
    )

    job_check_day_task = dag_helper.branch_op(
        task_id="py_check_day_task",
        doc="verfier le jour mardi ",
        python_callable=check_if_tuesday,
        process_date="pre_init[processDate]",
        action_if_ok='iam_credentials',
        action_if_error='py_ponderation-concepts'
    )

    job_iam_access_token_visionnages_ravenne = gcp_iam_access_token(my_dag_config.gcp_service_account)

    job_load_bq_ravenne_visionnages = dag_helper.run_bigquery_query_op(
        task_id="py_load_bq_ravenne_visionnages",
        doc="Chargement visionnages ravenne sur big query",
        job_name="bigquery-load-ravenne-visionnages",
        job_iam_access_token=job_iam_access_token_visionnages_ravenne
    )

    job_ponderation_concepts = dag_helper.submit_dataproc_job_op(
        task_id="py_ponderation-concepts",
        doc="Aggregation et pondération des concepts et channel/provider par time box",
        job_name="ponderation-concepts",
        job_type='hadoop_job',
        trigger_rule=trigger_rule.TriggerRule.NONE_FAILED
    )

    # Branche de calcul des statistiques
    job_stat_countconcepts = dag_helper.submit_dataproc_job_op(
        task_id="py_stat-countconcepts",
        doc="Statistiques par nombre de concepts",
        job_name="stat-countconcepts",
        job_type='hadoop_job'
    )

    job_stat_countbytimeslot = dag_helper.submit_dataproc_job_op(
        task_id="py_stat-countbytimeslot",
        doc="Statistiques par timeslots",
        job_name="stat-countbytimeslot",
        job_type='hadoop_job'
    )

    job_stat_countbyprovider = dag_helper.submit_dataproc_job_op(
        task_id="py_stat-countbyprovider",
        doc="Statistiques par channel/provider",
        job_name="stat-countbyprovider",
        job_type='hadoop_job'
    )

    # Branche de calcul et envoi des profils génétiques
    job_genetic_concat = dag_helper.submit_dataproc_job_op(
        task_id="py_genetic-concat",
        doc="Calcul des profils génétiques",
        job_name="genetic-concat",
        job_type='hadoop_job'
    )

    job_genetic_counterdiff = dag_helper.submit_dataproc_job_op(
        task_id="py_genetic-counterdiff",
        doc="Comparaison du nombre de profils génétiques par rapport à la semaine précédente",
        job_name="genetic-counterdiff",
        job_type='hadoop_job',
        capture=True
    )

    job_genetic_counters_check = dag_helper.branch_op(
        task_id="py_genetic_conters_check",
        doc="On vérifie si le nombre de profils génétiques est cohérent avec la semaine précédente",
        python_callable=func_counterscheck,
        counter_value='genetic-counterdiff[COUNTER_DIFF_IN_TARGET_DAY]',
        threshold='pre_init[genecticCounterDiffThreshold]',
        action_if_ok='py_de-anonymize-pns-sent',
        action_if_error='py_genetic_countererror_mail'
    )

    # genetic OK : cas où le nombre de profil est cohérent
    job_de_anonymize_pns_sent = dag_helper.submit_dataproc_job_op(
        task_id="py_de-anonymize-pns-sent",
        doc="Desanonymisation des profils génétiques à envoyer",
        job_name="de-anonymize-pns-sent",
        job_type='hadoop_job'
    )

    job_genetic_pnssend = dag_helper.submit_dataproc_job_op(
        task_id="py_genetic-pnssend",
        doc="Préparation des profils genetiques au format PIG pour envoi à PNS",
        job_name="genetic-pnssend",
        job_type='hadoop_job'
    )

    job_genetic_sendtopnsbucket = dag_helper.submit_bash_job_op(
        task_id="py_geneticsendtopnsbucket",
        doc="Copie des profils génétiques dans le bucket PNS",
        job_name="genetic-sendtopnsbucket",
    )

    job_genetic_createsuccess = dag_helper.submit_bash_job_op(
        task_id="py_geneticcreatesuccess",
        doc="Flag _SUCCESS créé dans le bucket des profils génétiques",
        job_name="genetic-createsuccess",
    )

    # genetic ERROR : cas où le nombre de profil n'est pas cohérent
    job_genetic_countererror_mail = dag_helper.send_mail_op(
        task_id="py_genetic_countererror_mail",
        doc="Envoi du mail d'erreur pour les profils génétiques",
        job_name="genetic-countererror-mail"
    )

    # Branche de calcul et envoi des profils types
    job_profiltype_tastebox = dag_helper.submit_dataproc_job_op(
        task_id="py_profiltype-tastebox",
        doc="Regroupement des concepts en vecteurs de consommations par timeslot pour calcul des profils types",
        job_name="profiltype-tastebox",
        job_type='hadoop_job'
    )

    job_profiltype_profils_verticales = dag_helper.submit_dataproc_job_op(
        task_id="py_profiltype-profils-verticales",
        doc="Détermination des profils types par comparaison des vecteurs de consommation avec les vecteurs types et calcul des scores des verticales (thématiques)",
        job_name="profiltype-profils-verticales",
        job_type='hadoop_job'
    )

    job_profiltype_counterdiff = dag_helper.submit_dataproc_job_op(
        task_id="py_profiltype-counterdiff",
        doc="Comparaison du nombre d'utilisateurs avec profils type par rapport à la semaine précédente",
        job_name="profiltype-counterdiff",
        job_type='hadoop_job',
        capture=True
    )

    job_profiltype_counters_check = dag_helper.branch_op(
        task_id="py_profiltype_conters_check",
        doc="On vérifie si le nombre de profils types est cohérent avec la semaine précédente",
        python_callable=func_counterscheck,
        counter_value='profiltype-counterdiff[COUNTER_DIFF_NB_USER_WITH_PROFIL]',
        threshold='pre_init[typeCounterDiffThreshold]',
        action_if_ok='py_profiltype_verticale_movefiles',
        action_if_error='py_profiltype_countererror_mail'
    )

    # type OK : cas où le nombre de profil est cohérent
    job_profiltype_verticale_movefiles = dag_helper.submit_bash_job_op(
        task_id="py_profiltype_verticale_movefiles",
        doc="Déplcement des profils type et score des verticales pour envoi par le Scoring",
        job_name="profiltype-vertical-movefiles",
    )

    # type ERROR : cas où le nombre de profil n'est pas cohérent
    job_profiltype_countererror_mail = dag_helper.send_mail_op(
        task_id="py_profiltype_countererror_mail",
        doc="Envoi du mail d'erreur pour les profils types",
        job_name="profiltype-countererror-mail"
    )

    # job_profiles_join_catalog = dag_helper.submit_dataproc_job_op(
    #     task_id="py_profiles-join-catalog",
    #     doc="Marqueurs profiles catalogue",
    #     job_name="marker-profiles-join-catalog",
    #     job_type='hadoop_job',
    #     trigger_rule=trigger_rule.TriggerRule.ALL_DONE
    # )

    job_scores_markers_profiles = dag_helper.submit_dataproc_job_op(
        task_id="py_scores-markers-profiles",
        doc="Marqueurs profiles calcul",
        job_name="marker-profiles-scores",
        job_type='hadoop_job'
    )

    job_markers_profiles_similarity = dag_helper.submit_dataproc_job_op(
        task_id="py_markers-profiles-similarity",
        doc="Marqueurs profiles calcul",
        job_name="marker-profiles-similarity",
        job_type='hadoop_job'
    )

    job_markerprofile_movefiles = dag_helper.submit_bash_job_op(
        task_id="py_markerprofile-movefiles",
        doc="Déplcement des markers profiles pour envoi par le Scoring",
        job_name="markerprofile-movefiles",
    )

    job_get_markers_profiles = dag_helper.submit_bash_job_op(
        task_id="py_get_markers_profiles",
        doc="Récuperation sur le BO des markers profiles",
        job_name='get-markers-profiles'
    )
    
    job_check_success = dag_helper.input_files_check_op(
        task_id="py_check_success",
        doc="""
            Vérification des flags success pour les profils génétiques et les profils types.
        """,
        job_name="check_success",
        trigger_rule=trigger_rule.TriggerRule.ALL_DONE
    )

    # Delete Cloud Dataproc cluster.
    delete_dataproc_cluster = dag_helper.delete_dataproc_cluster_op(
        # Setting trigger_rule to ALL_DONE causes the cluster to be deleted
        # even if the Dataproc job fails.
        trigger_rule=trigger_rule.TriggerRule.ALL_DONE
    )

    check_dag_run_task = dag_helper.check_dag_run_op(
        # Setting trigger_rule to ALL_DONE causes this task to be executed in all cases
        trigger_rule=trigger_rule.TriggerRule.ALL_DONE
    )

    # Define DAG dependencies.
    job_pre_init >> job_input_files_check >> create_dataproc_cluster >> job_init \
    >> job_filterAndTimeslot  \
    >> job_check_day_task >>[job_iam_access_token_visionnages_ravenne, job_ponderation_concepts]
    job_iam_access_token_visionnages_ravenne >> job_load_bq_ravenne_visionnages >> job_ponderation_concepts
    job_ponderation_concepts >> [job_stat_countconcepts, job_genetic_concat, job_profiltype_tastebox]

    job_stat_countconcepts >> job_stat_countbytimeslot >> job_stat_countbyprovider

    job_genetic_concat >> job_genetic_counterdiff >> job_genetic_counters_check \
        >> [job_de_anonymize_pns_sent, job_genetic_countererror_mail]
    job_de_anonymize_pns_sent >> job_genetic_pnssend >> job_genetic_sendtopnsbucket >> job_genetic_createsuccess

    job_profiltype_tastebox >> job_profiltype_profils_verticales >> job_profiltype_counterdiff \
        >> job_profiltype_counters_check >> [job_profiltype_verticale_movefiles, job_profiltype_countererror_mail]

    job_genetic_concat >> job_get_markers_profiles >> job_scores_markers_profiles >> job_markers_profiles_similarity  >> job_markerprofile_movefiles >> job_check_success

    [
        job_stat_countbyprovider,
        job_genetic_createsuccess,
        job_genetic_countererror_mail,
        job_profiltype_verticale_movefiles,
        job_profiltype_countererror_mail
    ] >> job_check_success >> delete_dataproc_cluster >> check_dag_run_task
