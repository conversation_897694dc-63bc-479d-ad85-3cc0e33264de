package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import com.orange.profiling.common.utils.FieldsUtils;

/**
 * Mapper implementation that processes viewing records (zaps) from the previous week
 * that were too short to meet duration thresholds but might continue into the current
 * week. This mapper is part of the cross-week viewing session handling in the
 * FilterAndTimeslot component.
 *
 * <h2>Purpose:</h2>
 * When a viewing session starts near the end of a week, it might continue into the
 * next week. These sessions might not meet the duration threshold in either week
 * individually, but could meet it when combined. This mapper processes these
 * potential cross-week sessions.
 *
 * <h2>Input Format:</h2>
 * Tab-separated values with the following fields:
 * <ol>
 *   <li>aid - Account ID</li>
 *   <li>vodOrLive - View type (VOD or LIVE)</li>
 *   <li>showDuration - Program duration in seconds</li>
 *   <li>providerId - Channel/Provider identifier</li>
 *   <li>contentId - Program/Asset identifier</li>
 *   <li>title - Program title</li>
 *   <li>externalEntertainmentId - External reference ID</li>
 *   <li>seasonName - Season information</li>
 *   <li>seriesName - Series information</li>
 *   <li>offerName - Offer information</li>
 *   <li>dayOfProgram - Day of program (YYYYMMDD for LIVE, 0 for VOD)</li>
 *   <li>beginZap - View start timestamp</li>
 *   <li>zapDuration - View duration in seconds</li>
 *   <li>concepts - Program concepts/categories</li>
 * </ol>
 *
 * <h2>Examples:</h2>
 * <h3>VOD Example:</h3>
 * <pre>
 * ********* TAB VOD TAB 7200 TAB 2424VIDEO TAB VOD1234 TAB Die Hard TAB 0
 * TAB ********** TAB 120 TAB categorie/fiction,ambiance et ton/cavachauffer
 * </pre>
 *
 * <h3>LIVE Example:</h3>
 * <pre>
 * ********* TAB LIVE TAB 7200 TAB 80 TAB ********* TAB La vie secrete des acariens TAB 20190708
 * TAB ********** TAB 120 TAB categorie/documentaire,ambiance et ton/ennui
 * </pre>
 *
 * <h2>Output:</h2>
 * <h3>Key:</h3>
 * Composite key combining program metadata:
 * <pre>aid TAB vodOrLive TAB showDuration TAB providerId TAB contentId TAB title TAB
 * externalEntertainmentId TAB seasonName TAB seriesName TAB offerName</pre>
 *
 * <h3>Value:</h3>
 * View details:
 * <pre>beginZap TAB zapDuration TAB concepts</pre>
 *
 * <h2>Processing Logic:</h2>
 * <ol>
 *   <li>Parses the input record into its component fields</li>
 *   <li>Validates field count ({@value #CSV_LENGTH} expected)</li>
 *   <li>Constructs composite key from program metadata</li>
 *   <li>Outputs key-value pair for aggregation with current week's data</li>
 * </ol>
 *
 * @see FilterAndTimeslotReducer For the aggregation of these records with current week's data
 * @see MainFilterAndTimeslot For the overall job configuration
 */
public class PreviousTooShortMapper extends Mapper<Object, Text, Text, Text> {

    private static final int CSV_LENGTH = 14;
    private static final int AID_IDX = 0;
    private static final int VODORLIVE_IDX = 1;
    private static final int SHOW_DURATION_IDX = 2;
    private static final int PROVIDER_ID_IDX = 3;
    private static final int CONTENT_ID_IDX = 4;
    private static final int TITLE_IDX = 5;
    private static final int EXTERNAL_ENTERTAINMENT_ID_IDX = 6;
    private static final int SEASON_NAME_IDX = 7;
    private static final int SERIES_NAME_IDX = 8;
    private static final int OFFER_NAME_IDX = 9;
    private static final int DAY_OF_PROGRAM_IDX = 10;

    private static final int BEGIN_ZAP_IDX = 11;
    private static final int ZAP_DURATION_IDX = 12;
    private static final int CONCEPTS_IDX = 13;

    private Text outputKey = new Text();
    private Text outputValue = new Text();

    /**
     * Processes a single record of a too-short viewing session from the previous week.
     * The method:
     * <ol>
     *   <li>Splits the input into fields using tab as delimiter</li>
     *   <li>Validates that all required fields are present</li>
     *   <li>Constructs a composite key containing program metadata:
     *     <ul>
     *       <li>Account information (aid)</li>
     *       <li>Program type (VOD/LIVE)</li>
     *       <li>Duration and timing information</li>
     *       <li>Content identification (provider, ID, title)</li>
     *       <li>Additional metadata (entertainment ID, season, series)</li>
     *     </ul>
     *   </li>
     *   <li>Creates a value containing viewing details:
     *     <ul>
     *       <li>View start time</li>
     *       <li>View duration</li>
     *       <li>Program concepts</li>
     *     </ul>
     *   </li>
     * </ol>
     *
     * <h2>Input Format Example:</h2>
     * <pre>
     * aid TAB VOD|LIVE TAB broadcastDuration TAB contentId TAB title TAB dayOfProgram
     * TAB beginZap TAB zapDuration TAB concepts
     * </pre>
     *
     * @param key Input key (not used)
     * @param value Tab-separated record of a too-short viewing session
     * @param context Hadoop context for output
     * @throws IOException If there is an error writing output
     * @throws InterruptedException If the task is interrupted
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {

        /** aid TAB VOD|LIVE TAB broadcastDuration TAB contentId TAB title TAB dayOfProgram
         * TAB beginZap TAB zapDuration TAB concepts
         * Exemple : ********* TAB VOD TAB 7200 TAB 2424VIDEO TAB VOD1234 TAB Die Hard TAB 0
         *           TAB ********** TAB 120 TAB categorie/fiction,ambiance et ton/cavachauffer
         * Exemple : ********* TAB LIVE TAB 7200 TAB 80 TAB ********* TAB La vie secrete des acariens TAB 20190708
         *           TAB ********** TAB 120 TAB categorie/documentaire,ambiance et ton/ennui
         */

        String[] csv = FieldsUtils.TAB_PATTERN.split(value.toString(), -1);
        if (csv.length >= CSV_LENGTH) {
            // Reforme la cle representant l'emission regardee
            outputKey.set(buildProgramKey(csv));
            outputValue.set(buildZapValue(csv));
            context.write(outputKey, outputValue);
        }
        else {
            context.getCounter(Counters.MALFORMED_PREVIOUS_ZAP).increment(1);
        }
    }

    /** Build complex key.
     * The key represent a program. The goal is to group all different zaps of one same program.
     *
     * @return aid TAB LIVE TAB broadcastDuration TAB contentId TAB title TAB dayOfProgram
     */
    private String buildProgramKey(String[] csv) {
        return String.join(FieldsUtils.TAB, csv[AID_IDX], csv[VODORLIVE_IDX], csv[SHOW_DURATION_IDX],
                csv[PROVIDER_ID_IDX], csv[CONTENT_ID_IDX], csv[TITLE_IDX], csv[EXTERNAL_ENTERTAINMENT_ID_IDX],
                csv[SEASON_NAME_IDX], csv[SERIES_NAME_IDX], csv[OFFER_NAME_IDX], csv[DAY_OF_PROGRAM_IDX]);
    }

    /** Build value.
     *
     * @return beginZap TAB zapDuration TAB concepts
     */
    private String buildZapValue(String[] csv) {
        return String.join(FieldsUtils.TAB,
                csv[BEGIN_ZAP_IDX], csv[ZAP_DURATION_IDX], csv[CONCEPTS_IDX]);
    }
}
