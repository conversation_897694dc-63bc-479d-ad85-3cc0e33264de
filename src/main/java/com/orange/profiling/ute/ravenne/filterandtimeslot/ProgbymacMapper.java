package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.IOException;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;

import com.orange.profiling.common.file.generated.Progbymac;
import com.orange.profiling.common.optin.OptinValue;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

/**
 * Mapper implementation for processing program viewing data from the Progbymac format.
 * This mapper processes LIVE viewing records, filtering and transforming them for 
 * aggregation in the FilterAndTimeslot component.
 *
 * <h2>Purpose:</h2>
 * The mapper serves several key functions:
 * <ol>
 *   <li>Filters out invalid or unwanted viewing records:
 *     <ul>
 *       <li>Records with missing or zero duration</li>
 *       <li>Interactive channel content (ISI channels)</li>
 *       <li>CANAL+ content</li>
 *       <li>Users who have opted out of recommendations</li>
 *     </ul>
 *   </li>
 *   <li>Handles cross-day program viewing:
 *     <ul>
 *       <li>Uses a 4-hour offset (4AM cutoff) for day boundaries</li>
 *       <li>Prevents splitting programs that run past midnight</li>
 *     </ul>
 *   </li>
 *   <li>Transforms viewing data into a format suitable for aggregation</li>
 * </ol>
 *
 * <h2>Input Format:</h2>
 * Tab-separated values with fields defined in {@link Progbymac}. Example:
 * <pre>
 * 119703108 1461250500 2100 80 divertissement/jeu BFT-007-001 TP FRANCE 3 FRANCE 3 TNT 
 * Chaînes de la TNT Harry IPTV_EZ Harry 2100 catégories/divertissement,sous-genres/jeu 
 * 229131281 episode-ou-etiez-vous-quand-season-7-episode-6-2010	Saison 7	Medium
 * </pre>
 *
 * <h2>Output Format:</h2>
 * <h3>Key:</h3>
 * Program metadata for aggregation:
 * <pre>aid TAB LIVE TAB broadcastDuration TAB channelId TAB programId TAB title TAB 
 * externalEntertainmentId TAB seasonName TAB seriesName TAB LIVE TAB dayOfProgram</pre>
 *
 * <h3>Value:</h3>
 * Viewing session details:
 * <pre>beginZap TAB zapDuration TAB concepts</pre>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Cross-day Program Handling: Uses 4AM as the day boundary to properly group 
 *       late-night viewing sessions</li>
 *   <li>Content Filtering: Excludes interactive channels and specific providers</li>
 *   <li>Opt-in Validation: Only processes data from users who have opted into 
 *       recommendations</li>
 *   <li>Duration Validation: Ensures viewing sessions have valid durations</li>
 * </ul>
 *
 * @see FilterAndTimeslotReducer For the aggregation of these records
 * @see TimeboxZapAggregation For the data model used in aggregation
 * @see Progbymac For the input data format definition
 */
public class ProgbymacMapper extends Mapper<Object, Text, Text, Text> {

    private static final long FOUR_HOURS_IN_SECONDS = 4 * DatesUtils.ONE_HOUR_SECONDS;
    private static final long MILLISECONDS = DatesUtils.MILLISECONDS;
    private static final int INTERACTIVE_CHANNEL_HASH_LENGTH = 32;
    private static final String INTERACTIVE_CHANNEL_ISI_PREFIX = "ISI-";
    private static final String CHANNEL_CANAL = "CANAL+";
    private static final DateTimeFormatter DATE_FORMATER = DatesUtils.DF_YYYYMMDD;
    private static final String TAB = FieldsUtils.TAB;

    private Text outputKey = new Text();
    private Text outputValue = new Text();
    private Progbymac progbymacLine = new Progbymac();
    private final OptinValue optinValue = new OptinValue();

    /**
     * Processes a single viewing record from the input data. The method performs several
     * validation and filtering steps before emitting the record for aggregation:
     * <ol>
     *   <li>Validates the input format and presence of required fields</li>
     *   <li>Checks for valid viewing duration</li>
     *   <li>Filters out interactive channels and CANAL+ content</li>
     *   <li>Verifies user opt-in status for recommendations</li>
     *   <li>Transforms the record into the aggregation format</li>
     * </ol>
     *
     * <h2>Input Example:</h2>
     * <pre>
     * 119703108 1461250500 2100 80 divertissement/jeu BFT-007-001 TP FRANCE 3 FRANCE 3 TNT 
     * Chaînes de la TNT Harry IPTV_EZ Harry 2100 catégories/divertissement,sous-genres/jeu 
     * 229131281 episode-ou-etiez-vous-quand-season-7-episode-6-2010	Saison 7	Medium
     * </pre>
     *
     * <h2>Processing Steps:</h2>
     * <ul>
     *   <li>Parses the input line into a {@link Progbymac} object</li>
     *   <li>Validates viewing duration using {@link #containsUsedData()}</li>
     *   <li>Filters unwanted content using {@link #isEpgIdToBeFiltered(Progbymac)}</li>
     *   <li>Verifies recommendation opt-in status</li>
     *   <li>Constructs program key and viewing value for output</li>
     * </ul>
     *
     * @param key Input key (not used)
     * @param value Tab-separated viewing record
     * @param context Hadoop context for output and counters
     * @throws IOException If there is an error writing output
     * @throws InterruptedException If the task is interrupted
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {

        // Exemple : 119703108 1461250500 2100 80 divertissement/jeu BFT-007-001 TP FRANCE 3 FRANCE 3 TNT 
        // Chaînes de la TNT Harry IPTV_EZ Harry 2100 catégories/divertissement,sous-genres/jeu 
        // 229131281 episode-ou-etiez-vous-quand-season-7-episode-6-2010	Saison 7	Medium
        progbymacLine.setValue(value.toString());
        if (progbymacLine.checkFormat() && containsUsedData()) {
            if(! isEpgIdToBeFiltered(progbymacLine)){
                optinValue.fromString(progbymacLine.getField(Progbymac.OPTIN));
                if (optinValue.isOptin(OptinValue.OPTIN_RECO)) {
                    // tri sur émission regardée
                    outputKey.set(buildProgramKey());
                    outputValue.set(buildZapValue());
                    context.write(outputKey, outputValue);
                } else {
                    context.getCounter(Counters.PROGBYMAC_OPTOUT_RECO).increment(1);
                }
            } else {
                context.getCounter(Counters.CHANNELID_NOT_FOUND).increment(1);
            }
        }
        else {
            context.getCounter(Counters.ZAP_WITHOUT_DURATION).increment(1);
        }
    }

    /**
     * Validates that the viewing record contains a valid duration value.
     * A record is considered valid if it has a positive non-zero duration.
     * This helps filter out incomplete or corrupted records.
     *
     * @return true if the record has a valid duration, false otherwise
     */
    private boolean containsUsedData() {
        int zapDuration = 0;
        try {
            zapDuration = Integer.parseInt(progbymacLine.getField(Progbymac.ZAPDURATION));
        }
        catch (Exception e) {
            return false;
        }
        return zapDuration > 0;
    }

    /**
     * Returns true if the progbymac line should be filtered.
     * 
     * We filter CANAL+ lines and interactive services lines, which do not have a real EPG-ID.
     * 
     * This method filters out two types of content:
     * <ol>
     *   <li>Interactive Services:
     *     <ul>
     *       <li>Channels with hashed names (32-character hash)</li>
     *       <li>ISI channels (prefixed with "ISI-")</li>
     *     </ul>
     *   </li>
     *   <li>CANAL+ Content:
     *     <ul>
     *       <li>Channels identified as "CANAL+"</li>
     *     </ul>
     *   </li>
     * </ol>
     *
     * @since 22/01/2024 to filter interactive service lines we have to take into account two formats :
     * - hashed channel name, ex. 88b59049e0dd6eccff00d8e09798ab42
     * - isi code, ex. ISI-25
     * 
     * @param progbymacLine The viewing record to check
     * @return true if the record should be filtered out, false if it should be kept
     */
    private boolean isEpgIdToBeFiltered(Progbymac progbymacLine) {
        String channelID = progbymacLine.getField(progbymacLine.FIELD_CHANNEL_ID);
        if (channelID.length() == INTERACTIVE_CHANNEL_HASH_LENGTH ||
                channelID.trim().startsWith(INTERACTIVE_CHANNEL_ISI_PREFIX) ||
                CHANNEL_CANAL.equals(channelID.trim())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Constructs the output key for a viewing record. The key combines program metadata
     * to enable grouping of all viewing sessions for the same program.
     *
     * <h2>Key Format:</h2>
     * <pre>aid TAB LIVE TAB broadcastDuration TAB channelId TAB programId TAB title TAB 
     * externalEntertainmentId TAB seasonName TAB seriesName TAB LIVE TAB dayOfProgram</pre>
     *
     * <h2>Key Components:</h2>
     * <ul>
     *   <li>Program Identification: channelId, programId, title</li>
     *   <li>Broadcast Information: broadcastDuration, dayOfProgram</li>
     *   <li>Series Information: externalEntertainmentId, seasonName, seriesName</li>
     *   <li>Viewing Context: aid (account ID), LIVE (view type)</li>
     * </ul>
     *
     * @return Tab-separated string containing program metadata
     * @see #getDayOfProgram(long) For day calculation logic
     */
    private String buildProgramKey() {
        String aid = progbymacLine.getField(Progbymac.AID);
        long beginZap = Long.parseLong(progbymacLine.getField(Progbymac.BEGINZAP));
        String dayOfProgram = getDayOfProgram(beginZap);
        String channelId = progbymacLine.getField(Progbymac.CHANNELID);
        String programId = progbymacLine.getField(Progbymac.PROGRAMID);
        String title = progbymacLine.getField(Progbymac.TITLE);
        String broadcastDuration = progbymacLine.getField(Progbymac.SHOWDURATION);
        String externalEntertainmentId = progbymacLine.getField(Progbymac.EXTERNALENTERTAINMENTID);
        String seasonName = progbymacLine.getField(Progbymac.ENTERTAINMENTSEASONNAME);
        String seriesName = progbymacLine.getField(Progbymac.ENTERTAINMENTSERIESNAME);
        return String.join(TAB, aid, TimeboxZapAggregation.LIVE, broadcastDuration,
                channelId, programId, title,externalEntertainmentId,seasonName,seriesName, TimeboxZapAggregation.LIVE, dayOfProgram);
    }

    /**
     * Calculates the reference day for a viewing session, adjusting for late-night
     * programming that crosses midnight. Uses a 4AM cutoff time to prevent splitting
     * programs that run past midnight.
     *
     * <h2>Algorithm:</h2>
     * <ol>
     *   <li>Converts Unix timestamp to DateTime</li>
     *   <li>Subtracts 4 hours to shift the day boundary</li>
     *   <li>Formats the result as YYYYMMDD</li>
     * </ol>
     *
     * For example, a program viewed at 1:30 AM will be grouped with the previous day's
     * content, as it's likely part of the previous day's programming schedule.
     *
     * @param beginZap Unix timestamp (seconds) when viewing began
     * @return Date string in YYYYMMDD format, adjusted for the 4AM cutoff
     */
    protected static String getDayOfProgram(long beginZap) {
        DateTime zapDate = new DateTime(beginZap * MILLISECONDS);
        return zapDate.minus(FOUR_HOURS_IN_SECONDS * MILLISECONDS).toString(DATE_FORMATER);
    }

    /**
     * Constructs the output value containing viewing session details.
     *
     * <h2>Value Format:</h2>
     * <pre>beginZap TAB zapDuration TAB concepts</pre>
     *
     * <h2>Value Components:</h2>
     * <ul>
     *   <li>beginZap: Unix timestamp when viewing began</li>
     *   <li>zapDuration: Duration of the viewing session in seconds</li>
     *   <li>concepts: Comma-separated list of program concepts/categories</li>
     * </ul>
     *
     * @return Tab-separated string containing viewing session details
     */
    private String buildZapValue() {
        String beginZap = progbymacLine.getField(Progbymac.BEGINZAP);
        String zapDuration = progbymacLine.getField(Progbymac.ZAPDURATION);
        String concepts = progbymacLine.getField(Progbymac.CONCEPTSLIST);

        return String.join(TAB, beginZap, zapDuration, concepts);
    }

}
