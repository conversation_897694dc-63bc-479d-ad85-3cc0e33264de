package com.orange.profiling.ute.ravenne.profiltype.tastebox;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import com.orange.profiling.ute.ravenne.file.Tastebox;
import com.orange.profiling.ute.ravenne.util.ValuedKeyUtils;

/**
 * Reducer implementation for the Ravenne Tastebox Processing System that aggregates
 * and normalizes concept weights by family. This class implements sophisticated
 * concept weight processing with family-based filtering and normalization.
 * 
 * Processing Flow:
 * 1. Concept Family Processing:
 *    - Loads concept-to-family mappings from configuration
 *    - Groups concepts by their family categories
 *    - Handles unknown concept families via counters
 * 
 * 2. Weight Aggregation:
 *    - Combines weights for repeated concepts
 *    - Maintains viewing duration totals
 *    - Groups concepts by family and weight
 * 
 * 3. Weight Filtering:
 *    - Keeps top N weights per family (configurable)
 *    - Removes concepts below minimum threshold
 *    - Normalizes weights to 0-10 range
 * 
 * Configuration Parameters:
 * - PARAM_NB_WEIGHT_LEVEL_KEPT_PER_FAMILY: Number of weight levels to keep per family (default: 2)
 * - PARAM_NORMALIZED_MAX_WEIGHT: Maximum normalized weight value (default: 10)
 * - PARAM_MINIMUM_WEIGHT_THRESHOLD: Minimum weight threshold (default: 2)
 * 
 * Input Format:
 * - Key: aid-timeslot
 * - Value: weighted_concepts TAB total_duration
 * 
 * Output Format:
 * - Key: aid
 * - Value: normalized_weighted_concepts TAB timeslot TAB total_duration
 * 
 * Key Features:
 * - Family-based concept grouping
 * - Hierarchical weight filtering
 * - Weight normalization
 * - Duration tracking
 * - Detailed error counting
 * 
 * <AUTHOR>
 */
public class TasteboxReducer extends Reducer<Text, Text, Text, Text> {

    private static final int CATALOG_CSV_LENGTH = 2;
    private static final int CATALOG_CONCEPT_IDX = 0;
    private static final int CATALOG_FAMILY_IDX = 1;

    private static final int KEY_AID_IDX = 0;
    private static final int KEY_TIMESLOT_IDX = 1;

    private static final int VAL_WEIGHTED_CONCEPTS_IDX = 0;
    private static final int VAL_TOTAL_DURATION_IDX = 1;

    private static final int PARAM_NB_WEIGHT_LEVEL_KEPT_PER_FAMILY = 2;
    private static final int PARAM_NORMALIZED_MAX_WEIGHT = 10;
    private static final int PARAM_MINIMUM_WEIGHT_THRESHOLD = 2;
    private Map<String, String> conceptFamily = new HashMap<>();

    private Text outputKey = new Text();
    private Text outputValue = new Text();
    private Tastebox tastebox = new Tastebox();

    /**
     * Initializes the reducer by loading concept family mappings from configuration.
     * The concept family file maps root concepts to their family categories
     * (e.g., FOND, FORME, COULEUR).
     * 
     * @param context Reducer context containing configuration
     * @throws IOException If there are issues reading configuration
     * @throws InterruptedException If the setup process is interrupted
     */
    @Override
    protected void setup(Reducer<Text, Text, Text, Text>.Context context) throws IOException, InterruptedException {

        super.setup(context);

        // fichier des familles de concept : FOND FORME COULEUR
        String conceptFamilyPath = context.getConfiguration().get(MainTastebox.CONCEPT_FAMILY);

        FsUtils fsUtils = new FsUtils(context.getConfiguration());
        try (BufferedReader br = fsUtils.getReaderForFile(conceptFamilyPath)) {
            String line;
            line = br.readLine();

            while (line != null) {
                String[] csv = FieldsUtils.TAB_PATTERN.split(line);
                if (csv.length >= CATALOG_CSV_LENGTH) {
                    String conceptRoot = csv[CATALOG_CONCEPT_IDX];
                    String family = csv[CATALOG_FAMILY_IDX];
                    conceptFamily.put(conceptRoot, family);
                }
                line = br.readLine();
            }
        }
    }

    /**
     * Processes grouped values for each aid-timeslot key to generate normalized
     * concept weights. The processing includes:
     * 1. Extracting user ID and timeslot from composite key
     * 2. Aggregating concept weights and durations
     * 3. Filtering concepts by weight and family
     * 4. Normalizing remaining weights
     * 5. Outputting final user profile data
     * 
     * @param key Composite key containing aid and timeslot
     * @param values Iterable of weighted concepts and durations
     * @param context Context for output and counter tracking
     * @throws IOException If there are issues writing output
     * @throws InterruptedException If the reduction process is interrupted
     */
    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {

        // decode key
        String[] complexKey = FieldsUtils.DASH_PATTERN.split(key.toString());
        String aid = complexKey[KEY_AID_IDX];
        String timebox = complexKey[KEY_TIMESLOT_IDX];

        TimeboxZapAggregation aggregatedConceptsWeightsAndDuration =
                aggregatesWeightedConceptsAndDuration(timebox, values);

        Map<String,Long> aggregatedWeightedConcepts =
                aggregatedConceptsWeightsAndDuration.getWeightedConcepts();

        Map<String,Long> filterdWeightedConcepts =
                filterWeightedConceptsByWeightsAndFamily(aggregatedWeightedConcepts, context);

        long totalDuration = aggregatedConceptsWeightsAndDuration.getLiveDuration();

        if (filterdWeightedConcepts.size() > 0) {
            String tasteBoxStr = ValuedKeyUtils.parseKeyValueToString(filterdWeightedConcepts);
            outputKey.set(aid);
            outputValue.set(tastebox.getStringValue(tasteBoxStr, timebox, Long.toString(totalDuration)));
            context.write(outputKey, outputValue);

        }
    }

    /**
     * Aggregates weighted concepts and durations from multiple input values.
     * For each timeslot:
     * 1. Combines weights for identical concepts
     * 2. Maintains total viewing duration
     * 3. Handles both live and VOD content
     * 
     * @param timebox Timeslot identifier
     * @param values Input values containing weighted concepts and durations
     * @return TimeboxZapAggregation containing combined weights and durations
     */
    private TimeboxZapAggregation aggregatesWeightedConceptsAndDuration(String timebox, final Iterable<Text> values) {

        TimeboxZapAggregation timeboxZapAggregation = new TimeboxZapAggregation(timebox);
        for (Text value : values) {
            String[] csv = FieldsUtils.TAB_PATTERN.split(value.toString());
            String weightedConceptsString = csv[VAL_WEIGHTED_CONCEPTS_IDX];
            timeboxZapAggregation.addConceptWeights(weightedConceptsString);

            long duration = Long.parseLong(csv[VAL_TOTAL_DURATION_IDX]);
            timeboxZapAggregation.addDuration(TimeboxZapAggregation.LIVE, duration);
        }
        return timeboxZapAggregation;
    }

    /**
     * Filters and normalizes weighted concepts based on family groupings.
     * Process:
     * 1. Builds tastebox structure grouping concepts by family and weight
     * 2. Filters to keep only top N weights per family
     * 3. Normalizes remaining weights to 0-10 range
     * 4. Removes concepts below minimum threshold
     * 
     * @param fullWeightedConcepts Map of all concepts and their weights
     * @param context Context for counter tracking
     * @return Filtered and normalized concept weights
     */
    private Map<String,Long> filterWeightedConceptsByWeightsAndFamily(
            Map<String,Long> fullWeightedConcepts, Context context) {

        Map<String,Map<Long,Set<String>>> tasteBox = buildTasteBox(fullWeightedConcepts, context);
        Map<String,Long> filteredWeightedConcepts = buildFilteredWeightedConcepts(tasteBox);
        return normalizeFilteredWeightedConcepts(filteredWeightedConcepts);
    }

    /**
     * Constructs the tastebox data structure that organizes concepts by family and weight.
     * Structure:
     * - Family -> Weight -> Set of Concepts
     * 
     * Processing:
     * 1. Extracts concept family
     * 2. Groups concepts by weight within family
     * 3. Tracks unknown families via counters
     * 4. Filters zero-weight concepts
     * 
     * @param fullWeightedConcepts Input concept weights
     * @param context Context for counter tracking
     * @return Tastebox structure mapping families to weight-concept groups
     */
    private Map<String, Map<Long, Set<String>>> buildTasteBox(
            Map<String, Long> fullWeightedConcepts, Context context) {

        Map<String, Map<Long, Set<String>>> tasteBox = new HashMap<>();

        for(Map.Entry<String, Long> entry: fullWeightedConcepts.entrySet()) {
            String concept = entry.getKey();
            Long weight = entry.getValue();
            if (weight > 0) {
                String family = getFamily(concept);
                if (family != null) {
                    addConceptToTasteBox(tasteBox, family, weight, concept);
                }
                else {
                    // unknown concept's family
                    // TODO add alternate output for logging purposes
                    context.getCounter(Counters.CONCEPT_WITHOUT_FAMILY).increment(1);
                }
            }
            else {
                context.getCounter(Counters.CONCEPT_WITH_NULL_WEIGHT).increment(1);
            }
        }

        return tasteBox;
    }

    /**
     * Extracts the family category from a concept name.
     * Concept Format: rootConcept/subConcept
     * Example: "sujets/sport" -> looks up family for "sujets"
     * 
     * @param concept Full concept path
     * @return Family name or null if not found
     */
    private String getFamily(String concept) {
        String[] conceptDetail = FieldsUtils.SLASH_PATTERN.split(concept);
        String rootConcept = conceptDetail[0];
        if (conceptFamily.containsKey(rootConcept)) {
            return conceptFamily.get(rootConcept);
        }
        return null;
    }

    /**
     * Adds a concept to the tastebox structure under its family and weight.
     * Creates necessary map entries if they don't exist.
     * 
     * @param tasteBox Target tastebox structure
     * @param family Concept's family category
     * @param weight Concept's weight value
     * @param concept Concept to add
     */
    private static void addConceptToTasteBox(Map<String, Map<Long, Set<String>>> tasteBox,
            String family, Long weight, String concept) {

        if (! tasteBox.containsKey(family)) {
            tasteBox.put(family, new HashMap<>());
        }
        Map<Long, Set<String>> familyWeightsMap = tasteBox.get(family);
        if (! familyWeightsMap.containsKey(weight)) {
            familyWeightsMap.put(weight, new HashSet<>());
        }
        Set<String> familyWeightConceptsSet = familyWeightsMap.get(weight);
        familyWeightConceptsSet.add(concept);
        familyWeightsMap.put(weight, familyWeightConceptsSet);
        tasteBox.put(family, familyWeightsMap);
    }

    /**
     * Builds filtered concept weights by selecting top N weights per family.
     * Selection Process:
     * 1. Sorts weights in descending order for each family
     * 2. Takes top N weights (N = PARAM_NB_WEIGHT_LEVEL_KEPT_PER_FAMILY)
     * 3. Includes all concepts at selected weight levels
     * 
     * @param tasteBox Organized concept structure by family and weight
     * @return Map of selected concepts to their weights
     */
    private static Map<String, Long> buildFilteredWeightedConcepts(Map<String, Map<Long, Set<String>>> tasteBox) {
        Map<String, Long> filteredWeightedConcepts = new TreeMap<>();

        // for each family
        for (Map<Long,Set<String>> familyWeightsMap: tasteBox.values()) {

            // we take weights of this family in reverse order
            List<Long> reverseSortedWeights = new ArrayList<>(familyWeightsMap.keySet());
            Collections.sort(reverseSortedWeights, Collections.reverseOrder());

            // And from higher weights (index = 0)
            // to the Nth weights (index = PARAM_NB_WEIGHT_LEVEL_KEPT_PER_FAMILY-1)
            //we had concepts and weights to the filteredWeightedConcepts
            int weightsSize = reverseSortedWeights.size();
            int weightLevel = 0;
            while( (weightLevel < PARAM_NB_WEIGHT_LEVEL_KEPT_PER_FAMILY) && (weightLevel < weightsSize) ) {
                Long weight = reverseSortedWeights.get(weightLevel);
                for(String concept: familyWeightsMap.get(weight)) {
                    filteredWeightedConcepts.put(concept, weight);
                }
                weightLevel++;
            }
        }
        return filteredWeightedConcepts;
    }

    /**
     * Normalizes concept weights to a 0-10 range and applies minimum threshold.
     * Process:
     * 1. Finds maximum weight in input set
     * 2. Scales all weights relative to maximum
     * 3. Multiplies by PARAM_NORMALIZED_MAX_WEIGHT
     * 4. Filters weights below PARAM_MINIMUM_WEIGHT_THRESHOLD
     * 
     * @param filteredWeightedConcepts Input concept weights
     * @return Normalized and filtered concept weights
     */
    private static Map<String, Long> normalizeFilteredWeightedConcepts(
                    Map<String, Long> filteredWeightedConcepts) {

        if (filteredWeightedConcepts.isEmpty()) {
            return new HashMap<>();
        }
        final long finalTopWeight = Collections.max(filteredWeightedConcepts.values());
        return filteredWeightedConcepts.entrySet().stream()
                // normalize values
                .map(entry -> new AbstractMap.SimpleEntry<>(
                            entry.getKey(),
                            entry.getValue() * PARAM_NORMALIZED_MAX_WEIGHT / finalTopWeight)
                )
                // filter values below a given threshold
                .filter(entry -> entry.getValue() >= PARAM_MINIMUM_WEIGHT_THRESHOLD)
                // return a map of concepts/value pairs for the tasteBox
                .collect(Collectors.toMap(
                                AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue,
                                (v1, v2) -> v1, TreeMap::new)
                );
    }

}
