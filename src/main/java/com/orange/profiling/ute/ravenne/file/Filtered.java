package com.orange.profiling.ute.ravenne.file;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import com.orange.profiling.common.file.AbstractProfilingHdfsFile;
import com.orange.profiling.common.utils.FieldsUtils;


/**
 * File /user/profiling-ute/private/generated/ute/ravenne/filtered/[YYYY/WW]/selected/part-r-*
 * Generated by Ravenne
 * Aggregation of zaps by contentid
 *
 * Format
 *   aid TAB concepts TAB day TAB period TAB timeslot TAB vodorlive TAB zapduration TAB zaptime TAB provider TAB contentId TAB title
 *
 *     - aid : aid of the client
 *     - concepts : list of concepts
 *     - day : day of view
 *     - period : period of view
 *     - timeslot : timeslot of view
 *     - vodorlive : vor or live
 *     - zapduration : total duration of view
 *     - zaptime : time of view
 *     - provider : channelId for live or serviceCode for onDemand
 *     - contentId : programId for live or externalAssetId for onDemand
 *     - title : title of program
 *     - externalEntertainmentId : external id of an entertainment metadata
 *     - seasonName : Name of the season of an entertainment
 *     - seriesName : Name of the serie of an entertainment
 *
 * Pig :
 *     filtered = LOAD '/user/profiling-ute/private/generated/ute/ravenne/filtered/[YYYY/WW]/selected/part-r-*'
 *                USING PigStorage('\t') AS (aid:chararray, concepts:chararray,
 *                      day:long, period:long, timeslot:long, vodorlive:chararray, zapduration:long, zaptime:long
 *                      provider:chararray, contentId:chararray, title:chararray);
 *
 */
public class Filtered extends AbstractProfilingHdfsFile {

    public static final int AID = 0;
    public static final int CONCEPTS = 1;
    public static final int DAY = 2;
    public static final int PERIOD = 3;
    public static final int TIMESLOT = 4;
    public static final int VODORLIVE = 5;
    public static final int ZAPDURATION = 6;
    public static final int ZAPTIME = 7;
    public static final int PROVIDER = 8;
    public static final int CONTENT_ID = 9;
    public static final int TITLE = 10;
    public static final int EXTERNALENTERTAINMENTID = 11;
    public static final int SEASONNAME = 12;
    public static final int SERIESNAME = 13;
    public static final int OFFERNAME = 14;


    private static final Map<String, Integer> FIELDS = new HashMap<>();
    static {
        FIELDS.put("aid", AID);
        FIELDS.put("concepts", CONCEPTS);
        FIELDS.put("day", DAY);
        FIELDS.put("period", PERIOD);
        FIELDS.put("timeslot", TIMESLOT);
        FIELDS.put("vodorlive", VODORLIVE);
        FIELDS.put("zapduration", ZAPDURATION);
        FIELDS.put("zaptime", ZAPTIME);
        FIELDS.put("provider", PROVIDER);
        FIELDS.put("contentId", CONTENT_ID);
        FIELDS.put("title", TITLE);
        FIELDS.put("externalEntertainmentId", EXTERNALENTERTAINMENTID);
        FIELDS.put("seasonName", SEASONNAME);
        FIELDS.put("seriesName", SERIESNAME);
        FIELDS.put("offerName", OFFERNAME);
    }

    private static final String SEPARATOR = FieldsUtils.TAB;
    private static final Pattern PATTERN = FieldsUtils.TAB_PATTERN;

    @Override
    public Map<String, Integer> getFields() {
        return FIELDS;
    }

    @Override
    public String getSeparator() {
        return SEPARATOR;
    }

    @Override
    public Pattern getSplitPattern() {
        return PATTERN;
    }

    @Override
    public boolean checkFormat() {
        // At least we must have vod or live
        return (getLength() > PROVIDER);
    }
}
