package com.orange.profiling.ute.ravenne.ponderation;

import static com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation.LIVE;
import static com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation.VOD;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import com.orange.profiling.common.file.generated.Optin;
import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.optin.OptinMapper;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.OrangeTimeslotUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import com.orange.profiling.ute.ravenne.file.ConceptsWeights;
import com.orange.profiling.ute.ravenne.util.TopValuedKeySelector;
import com.orange.profiling.ute.ravenne.util.ValuedKeyUtils;

/**
 * A Hadoop Reducer that processes and aggregates viewing data to generate user profiles
 * based on their viewing patterns. This reducer is a key component of the Ravenne profiling
 * system, handling concept weighting, duration aggregation, and channel/provider statistics.
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Concept Processing:
 *     <ul>
 *       <li>Aggregates and weights concepts across different timeboxes</li>
 *       <li>Applies hierarchical weighting to concept relationships</li>
 *       <li>Normalizes concept names to root/leaf format</li>
 *       <li>Limits the number of concepts per profile</li>
 *     </ul>
 *   </li>
 *   <li>Duration Tracking:
 *     <ul>
 *       <li>Aggregates viewing durations for LIVE and VOD content</li>
 *       <li>Maintains channel and provider statistics</li>
 *       <li>Tracks replay viewing patterns</li>
 *     </ul>
 *   </li>
 *   <li>Special Processing:
 *     <ul>
 *       <li>Handles youth content separately with dedicated timeboxes</li>
 *       <li>Tracks replay viewing patterns (24h and 7-day windows)</li>
 *       <li>Maintains program count statistics</li>
 *     </ul>
 *   </li>
 * </ul>
 *
 * <h2>Input Format:</h2>
 * Key: Account ID (AID)
 * Value: Tab-separated string containing:
 * <pre>
 * [concepts]\t[daytimeslot]\t[type]\t[duration]\t[weight]\t[provider]\t[offerName]\t[viewAge]
 * </pre>
 *
 * <h2>Output:</h2>
 * Generates multiple output files:
 * <ul>
 *   <li>Weighted concepts per timebox</li>
 *   <li>Best channels and providers</li>
 *   <li>Program count statistics</li>
 * </ul>
 *
 * <h2>Configuration:</h2>
 * Supports configuration for:
 * <ul>
 *   <li>Maximum number of concepts ({@value #CONF_TOP_CONCEPTS})</li>
 *   <li>Maximum number of live channels ({@value #CONF_TOP_LIVE_CHANNELS})</li>
 *   <li>Maximum number of VOD providers ({@value #CONF_TOP_OD_PROVIDERS})</li>
 * </ul>
 *
 * @see TimeboxZapAggregation For the aggregation data structure
 * @see ConceptPonderator For the concept weighting logic
 * @see FilteredInputMapper For the input data format
 */
public class PonderationConceptsReducer  extends Reducer<Text, Text, Text, Text> {

    private static final String PART = "/part";
    public static final String NB_PROGRAM_COUNTER_GROUP = "NB_PROGRAM";
    public static final String NB_PROGRAM_COUNTER_TIMESLOT = NB_PROGRAM_COUNTER_GROUP+"_T";
    public static final String NB_PROGRAM_LESS_THAN_FIVE = "_1_LESS_THAN_FIVE";
    public static final String NB_PROGRAM_FIVE_TO_TEN = "_2_FIVE_TO_TEN";
    public static final String NB_PROGRAM_MORE_THAN_TEN = "_3_MORE_THAN_TEN";

    public static final String COUNTER_ORDER_AND_WEIGHT_PREFIX = "ORDER_AND_WEIGHT_";
    public static final String COUNTER_ORDER_AND_WEIGHT_FORMAT = COUNTER_ORDER_AND_WEIGHT_PREFIX+"%s_%s";

    public static final String CONF_TOP_CONCEPTS = "topConcepts";
    public static final String CONF_TOP_LIVE_CHANNELS = "topLiveChannels";
    public static final String CONF_TOP_OD_PROVIDERS = "topOdProviders";

    private static final int CONCEPTS_IDX = 0;
    private static final int DAYTIMESLOT_IDX = 1;
    private static final int LIVEORVOD_IDX = 2;
    private static final int ZAP_DURATION_IDX = 3;
    private static final int WEIGHT_IDX = 4;
    private static final int PROVIDER_IDX = 5;
    private static final int OFFERNAME_IDX = 6;
    private static final int NB_DAYS_VIEW_IDX = 7;

    private static final int DEFAULT_TOP_CONCEPTS = 20;
    private static final int DEFAULT_TOP_LIVE_CHANNELS = 5;
    // Par defaut, on ne veut pas limiter la profondeur de récupération des catchup channel pour les timebox VOD,
    // mais on risque d'en recuperer trop et il y a une limite à la taille du champ dans Advise (16k).
    private static final int DEFAULT_TOP_OD_PROVIDERS = 20;

    /* concept list of youth program */
    private static final String[] CONCEPTS_JEUNESSE = {
            "audience/pour enfants",
            "audience/pour les tout-petits",
            "audience/pour les petits",
            "catégories/programme jeunesse",
            "genres/animation"
    };

    private MosWriter mos;
    private ConceptsWeights conceptsWeights = new ConceptsWeights();
    private TopValuedKeySelector topConcepts;
    private TopValuedKeySelector topLiveChannels;
    private TopValuedKeySelector topOdProviders;

    private static final int INDEX_TAG = 0;
    private String aid = "";

    private Map<String, TimeboxZapAggregation> zapAggregationMap = new HashMap<>();
    private Map<String, Integer> nbProgramByTimeslot = new HashMap<>();
    private boolean replayViews24H = false;
    private Set<String> replayViews7Days = new HashSet<>();

    private static final Optin optin = new Optin();

    /**
     * Initializes the reducer with the given configuration.
     *
     * @param context the reducer context
     */
    @Override
    public final void setup(final Context context) {
        getMosWriter().open(context);
        buildTopSelector(context.getConfiguration());
    }

    /**
     * Builds the top selectors for concepts, live channels, and VOD providers based on the given configuration.
     *
     * @param configuration the reducer configuration
     */
    protected void buildTopSelector(final Configuration configuration) {
        topConcepts = new TopValuedKeySelector(
                configuration.getInt(CONF_TOP_CONCEPTS, DEFAULT_TOP_CONCEPTS));

        topLiveChannels = new TopValuedKeySelector(
                configuration.getInt(CONF_TOP_LIVE_CHANNELS, DEFAULT_TOP_LIVE_CHANNELS));

        topOdProviders = new TopValuedKeySelector(
                configuration.getInt(CONF_TOP_OD_PROVIDERS, DEFAULT_TOP_OD_PROVIDERS));

    }

    /**
     * Reduces the given key-value pairs and generates output.
     *
     * @param key the reducer key
     * @param values the reducer values
     * @param context the reducer context
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {

        clearPreviousData();

        if(storeOptinTVKeyAndZap(values, context)){
            // This counter is used in step stat-countbytimeslot
            context.getCounter(Counters.WEIGHTED_AID).increment(1);

            countNbProgram(context, nbProgramByTimeslot);
            treatmentOfZapAggregation(context, zapAggregationMap);
            countNbFoyerReplay(context);
        }
    }

    /**
     * Clears the previous data.
     */
    private void clearPreviousData() {
        zapAggregationMap.clear();
        nbProgramByTimeslot.clear();
        replayViews24H = false;
        replayViews7Days.clear();
    }

    /**
     * Stores the opt-in TV key and zap data.
     *
     * @param values the reducer values
     * @param context the reducer context
     * @return true if the opt-in TV key is found, false otherwise
     */
    private boolean storeOptinTVKeyAndZap(Iterable<Text> values, Context context) {
        boolean isOptin = false;

        for (Text value : values) {
            String[] zap = FieldsUtils.TAB_PATTERN.split(value.toString(), -1);

            if(zap[INDEX_TAG].equals(OptinMapper.OPTIN_TAG)){
                optin.setValue(excludeFirstElement(zap));
                if(optin.isOptinTv()){
                    aid = optin.getField(Optin.AID_HASH);
                    isOptin = true;
                }
            }else{
                //catchup channel id => VOD  / channel id => LIVE
                zap[PROVIDER_IDX] = transformChannelProvider(context, zap);

                aggregateZap(zapAggregationMap, zap);

                nbProgramByTimeslot.merge(zap[DAYTIMESLOT_IDX], 1, Integer::sum);

                isViewReplay(zap);
            }
        }

        return isOptin;
    }

    /**
     * Checks if the given zap data represents a replay view.
     *
     * @param zap the zap data
     */
    private void isViewReplay(String[] zap) {
        if (zap[OFFERNAME_IDX].equalsIgnoreCase("TVOD") && Integer.parseInt(zap[NB_DAYS_VIEW_IDX]) <= 7) {
            // Replay viewed since 24h
            if (Integer.parseInt(zap[NB_DAYS_VIEW_IDX]) == 1) {
                replayViews24H = true;

            } // Replay viewed since 7 days
            if(Integer.parseInt(zap[NB_DAYS_VIEW_IDX]) >= 1 && Integer.parseInt(zap[NB_DAYS_VIEW_IDX]) <= 7) {
                replayViews7Days.add("replayViewsSince"+zap[NB_DAYS_VIEW_IDX]+"Days");
            }
        }
    }

    /**
     * Excludes the first element from the given array.
     *
     * @param zap the array
     * @return the array without the first element
     */
    private String excludeFirstElement(String[] zap){
        return String.join(FieldsUtils.TAB, Arrays.copyOfRange(zap,1, zap.length - 1));
    }

    /**
     * Transforms the channel provider based on the given context and zap data.
     *
     * For VOD, provider has format ServiceCode-Channel|catchupChannel.
     * catchupChannel is only filled for replay : it's the value to use when it exists
     * ServiceCode-Channel is used if we don't have catchupChannel
     *
     * @param context the reducer context
     * @param zap the zap data
     * @return the transformed channel provider
     */
    private static String transformChannelProvider(Reducer<Text, Text, Text, Text>.Context context, String[] zap) {

        String liveOrVod = zap[LIVEORVOD_IDX];
        String channelProvider = zap[PROVIDER_IDX];
        String returnProvider = channelProvider;
        if (VOD.equals(liveOrVod)) {
            String[] parts = FieldsUtils.PIPE_PATTERN.split(channelProvider);
            if(parts.length>1 && !parts[1].isEmpty()) {
                // catchup channel
                returnProvider = parts[1];
                context.getCounter(Counters.NB_CATCHUP_CHANNEL_ID).increment(1);
            }
            else {
                // ServiceCode with channel info
                returnProvider = "";
                context.getCounter(Counters.NB_VOD_MISSING_CATCHUP_CHANNEL_ID).increment(1);
            }
        }
        return returnProvider;
    }

    /**
     * Aggregates the zap data for the given timebox.
     * While reading mapper values add concepts and duration for daytimeslot
     *
     * 1/ Add a new conceptList to the weighted concepts of the daytimeslot
     *
     * The weight of a concept is the number of times it was added for a specific daytimeslot.
     *
     * If we have
     *   d1t2 => concept1,concept2,concept3
     *   d1t2 => concept2,concept3
     *   d1t2 => concept2,concept4
     *   d2t3 => concept1concept3
     *   d2t3 => concept1,concept2
     *
     * We will end with
     *   d1t2 => concept1=1,concept2=3,concept3=2,concept4=1
     *   d2t3 => concept1=2,concept2=1,concept3=1
     *
     * 2/ add duration for LIVE or for VOD to durations of the  daytimeslot
     *
     * If we have
     *   d3t6 => LIVE 1200
     *   d3t6 => LIVE 600
     *   d3t6 => VOD 150
     *   d5t10 => LIVE 600
     *   d5t10 => VOD 1800
     *   d5t10 => LIVE 800
     *   d5t10 => VOD 2400
     *
     * We will end with
     *   d3t6 => LIVE=1800 & VOD=150
     *   d5t10 => LIVE=1400 & VOD=4200
     *
     * 3/ add liveChannel or odProvider duration to the daytimeslot
     *
     * If we have
     *   d3t6 => LIVE 1200 channel1
     *   d3t6 => LIVE 600 channel2
     *   d3t6 => LIVE 400 channel1
     *   d3t6 => VOD 150 providerA
     *   d3t6 => VOD 250 providerB
     *   d3t6 => VOD 150 providerB
     *
     * We will end with
     *   d3t6 => channel1 = 1600 , channel2 = 600
     *   d3t6 => providerA = 150 , providerB = 400
     *
     * @param zapAggregationByTimeslot
     * @param zap
     * @return the timeslot corresponding to the value
     */
    protected static void aggregateZap(
            Map<String, TimeboxZapAggregation> zapAggregationByTimeslot, String[] zap) {

        boolean isProgramJeunesse = containsConceptsJeunesse(zap[CONCEPTS_IDX]);

        if (isProgramJeunesse) {
            aggregateFullZapToTimebox(zapAggregationByTimeslot,
                    OrangeTimeslotUtils.JEUNESSE, zap);

            String daytimeslot = zap[DAYTIMESLOT_IDX];

            List<String> containingTimeboxes =
                    OrangeTimeslotUtils.getContainingTimeboxesKids(daytimeslot);

            for (String timebox : containingTimeboxes) {
                aggregateFullZapToTimebox(zapAggregationByTimeslot, timebox, zap);
            }

        } else {
            String daytimeslot = zap[DAYTIMESLOT_IDX];
            String liveOrVod = zap[LIVEORVOD_IDX];

            List<String> containingTimeboxes =
                    OrangeTimeslotUtils.getContainingTimeboxes(daytimeslot, LIVE.equals(liveOrVod));

            for (String timebox : containingTimeboxes) {
                aggregateFullZapToTimebox(zapAggregationByTimeslot, timebox, zap);
            }

            List<String> containingTimeboxesOfContraryType =
                    OrangeTimeslotUtils.getContainingTimeboxes(daytimeslot, VOD.equals(liveOrVod));

            for (String timeboxOfContraryType : containingTimeboxesOfContraryType) {
                aggregateOnlyZapDurationToTimebox(zapAggregationByTimeslot, timeboxOfContraryType, zap);
            }
        }
    }

    /**
     * Checks if the given concepts contain youth concepts.
     *
     * @param concepts the concepts
     * @return true if the concepts contain youth concepts, false otherwise
     */
    public static Boolean containsConceptsJeunesse(String concepts) {
        Set<String> conceptList = new HashSet<>(Arrays.asList(FieldsUtils.COMMA_PATTERN.split(concepts)));
        for (String entry : CONCEPTS_JEUNESSE) {
            if (conceptList.contains(entry)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Aggregates the full zap data to the given timebox.
     *
     * @param zapAggregationByTimeslot the zap aggregation map
     * @param timebox the timebox
     * @param zap the zap data
     */
    private static void aggregateFullZapToTimebox(
            Map<String, TimeboxZapAggregation> zapAggregationByTimeslot, String timebox, String[] zap) {

        String liveOrVod = zap[LIVEORVOD_IDX];
        String channelProvider = zap[PROVIDER_IDX];
        long zapDuration = Long.parseLong(zap[ZAP_DURATION_IDX]);
        String concepts = zap[CONCEPTS_IDX];
        long weight = Long.parseLong(zap[WEIGHT_IDX]);

        TimeboxZapAggregation timeboxInfo =
                zapAggregationByTimeslot.getOrDefault(timebox, new TimeboxZapAggregation(timebox));

        timeboxInfo.addConcepts(concepts, weight);
        timeboxInfo.addDuration(liveOrVod, zapDuration);
        if (!channelProvider.isEmpty()) {
            timeboxInfo.addChannelProviderDuration(liveOrVod, channelProvider, zapDuration);
        }

        zapAggregationByTimeslot.put(timebox, timeboxInfo);
    }

    /**
     * Aggregates only the zap duration to the given timebox.
     *
     * @param zapAggregationByTimeslot the zap aggregation map
     * @param timebox the timebox
     * @param zap the zap data
     */
    private static void aggregateOnlyZapDurationToTimebox(
            Map<String, TimeboxZapAggregation> zapAggregationByTimeslot, String timebox, String[] zap) {

        String liveOrVod = zap[LIVEORVOD_IDX];
        long zapDuration = Long.parseLong(zap[ZAP_DURATION_IDX]);

        TimeboxZapAggregation timeboxInfo =
                zapAggregationByTimeslot.getOrDefault(timebox, new TimeboxZapAggregation(timebox));

        timeboxInfo.addDuration(liveOrVod, zapDuration);

        zapAggregationByTimeslot.put(timebox, timeboxInfo);
    }

    /**
     * Counts the number of programs for the given context and timeslot.
     *
     * @param context the reducer context
     * @param nbProgramByTimeslot the program count map
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    private void countNbProgram(Reducer<Text, Text, Text, Text>.Context context,
            Map<String, Integer> nbProgramByTimeslot)
                    throws IOException, InterruptedException {

        for(Map.Entry<String, Integer> timeslotNbProgram: nbProgramByTimeslot.entrySet()) {
            String dayTimeslot = timeslotNbProgram.getKey();
            Integer nbProgram = timeslotNbProgram.getValue();
            incrementNbProgramCounter(context, dayTimeslot, nbProgram);
            getMosWriter().write(MainPonderationConcepts.OUT_NB_PROGRAM, aid,
                    String.join(FieldsUtils.TAB, dayTimeslot, Integer.toString(nbProgram)),
                    MainPonderationConcepts.OUT_NB_PROGRAM+PART);
        }
    }

    /**
     * Counts the number of foyer replays for the given context.
     *
     * @param context the reducer context
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    private void countNbFoyerReplay(Reducer<Text, Text, Text, Text>.Context context)
            throws IOException, InterruptedException {
        if(replayViews24H) {
            context.getCounter(Counters.NB_FOYER_REPLAY_24H_COUNTER).increment(1);
        }
        if(replayViews7Days.size() >= 4) {
            context.getCounter(Counters.NB_FOYER_REPLAY_4DAYS_PER_7_COUNTER).increment(1);
        }
    }

    /** Increment counter on nbProgram by timeslot
     *
     * @param context
     * @param dayTimeslot
     * @param nbProgram
     */
    private void incrementNbProgramCounter(
            Reducer<Text, Text, Text, Text>.Context context,
            String dayTimeslot, Integer nbProgram) {

        String counterGlobalName = NB_PROGRAM_COUNTER_GROUP;
        String numTimeslot = String.format("%02d",
                Integer.valueOf(OrangeTimeslotUtils.getTimeslotIndex(dayTimeslot)));
        String counterTimeslotName = NB_PROGRAM_COUNTER_TIMESLOT+numTimeslot;
        String categoryNbProgram = getCategoryNbProgram(nbProgram);
        counterGlobalName += categoryNbProgram;
        counterTimeslotName += categoryNbProgram;
        context.getCounter(NB_PROGRAM_COUNTER_GROUP, counterGlobalName).increment(1);
        context.getCounter(NB_PROGRAM_COUNTER_TIMESLOT, counterTimeslotName).increment(1);
    }


    protected static String getCategoryNbProgram(Integer nbProgram) {
        String categoryNbProgram = "";
        if (nbProgram<5) {
            categoryNbProgram = NB_PROGRAM_LESS_THAN_FIVE;
        }
        else if (nbProgram < 11) {
            categoryNbProgram = NB_PROGRAM_FIVE_TO_TEN;
        }
        else {
            categoryNbProgram = NB_PROGRAM_MORE_THAN_TEN;
        }
        return categoryNbProgram;
    }

    /**
     * Treats the zap aggregation for the given context and timebox.
     * With zapAggregation calculated, for each timebox we have :
     *   - a ponderated list of concepts : concept1=100,concept2=200,...
     *   - the total live duration and the total vod duration
     *   - the durations by liveChannel or odProvider
     *
     *  The initial ponderation (number of occurrences of each concepts) is modified
     *  to only take into account the leaf weight.
     *  The concepts list is "normalized" : it means concepts name only contains root/leaf
     *  (root/parent1/parent2/leaf ==> root/leaf)
     *
     *
     *  The outputs are written in an optin directory
     *
     *
     * @param context the reducer context
     * @param zapAggregationByTimeslot the zap aggregation map
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    private void treatmentOfZapAggregation(Reducer<Text, Text, Text, Text>.Context context,
            Map<String, TimeboxZapAggregation> zapAggregationByTimeslot)
                    throws IOException, InterruptedException {

        for(Map.Entry<String, TimeboxZapAggregation> entry: zapAggregationByTimeslot.entrySet()) {
            String timebox = entry.getKey();
            TimeboxZapAggregation zapAggregation = entry.getValue();

            ponderateConcepts(zapAggregation);
            zapAggregation.normalizeConceptsName();
            limitConceptNumber(zapAggregation);

            incrementTimeboxCounters(context, zapAggregation);
            incrementJeunessesProfilCounters(context, zapAggregation);

            writeConceptsWeights(timebox, zapAggregation);
            writeBestChannels(timebox, zapAggregation);
            writeBestProviders(timebox, zapAggregation);
        }
    }

    /**
     * Ponderates the concepts for the given zap aggregation.
     *
     * @param zapAggregation the zap aggregation
     */
    private static void ponderateConcepts(TimeboxZapAggregation zapAggregation) {
        ConceptPonderator conceptPonderator = new ConceptPonderator(zapAggregation.getWeightedConcepts());
        Map<String, Long> weightedConceptMap = conceptPonderator.ponderatesParentsWithChildWeight();
        zapAggregation.setWeightedConcepts(weightedConceptMap);
    }

    /**
     * Limits the number of concepts for the given zap aggregation.
     *
     * @param zapAggregation the zap aggregation
     */
    protected void limitConceptNumber(TimeboxZapAggregation zapAggregation) {
        Map<String,Long> topPonderatedConcepts =
                topConcepts.getTopKeys(zapAggregation.getWeightedConcepts());
        zapAggregation.setWeightedConcepts(topPonderatedConcepts);
    }

    /**
     * Increments the timebox counters for the given context and zap aggregation.
     *
     * @param context the reducer context
     * @param zapAggregation the zap aggregation
     */
    private static void incrementTimeboxCounters(final Context context, TimeboxZapAggregation zapAggregation) {
        int nbConcepts = zapAggregation.getWeightedConcepts().size();
        if (nbConcepts > 0) {
            context.getCounter(Counters.NB_GENETIC_PROFILES).increment(1);
            context.getCounter(Counters.NB_CONCEPTS_TOTAL).increment(nbConcepts);
        }
    }

    /**
     * Increments the jeunesses profil counters for the given context and zap aggregation.
     *
     * @param context the reducer context
     * @param zapAggregation the zap aggregation
     */
    private static void incrementJeunessesProfilCounters(final Context context , TimeboxZapAggregation zapAggregation){
        String timebox = zapAggregation.getTimebox();
        if(timebox.equals(OrangeTimeslotUtils.JEUNESSE)){
            context.getCounter(Counters.NB_PROFILES_JEUNESSE).increment(1);
        }
        if(timebox.matches(OrangeTimeslotUtils.JEUNESSE_REGEX)){
            context.getCounter(Counters.NB_PROFILES_JEUNESSE_DAYS).increment(1);
        }
    }

    /**
     * Writes the concepts weights for the given timebox and zap aggregation.
     *
     * @param timebox the timebox
     * @param zapAggregation the zap aggregation
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    protected void writeConceptsWeights(String timebox, TimeboxZapAggregation zapAggregation)
            throws IOException, InterruptedException {

        String timeboxtype = OrangeTimeslotUtils.getTimeboxType(timebox);
        String ponderatedConcepts = zapAggregation.getWeightedConceptsString();

        Long liveDuration = zapAggregation.getLiveDuration();
        Long vodDuration = zapAggregation.getVodDuration();

        if(ponderatedConcepts.trim().length() > 0){
            getMosWriter().write(MainPonderationConcepts.OUT_CONCEPTS_WEIGHTS, aid,
                    conceptsWeights.getStringValue(timebox, ponderatedConcepts,
                            Long.toString(liveDuration), Long.toString(vodDuration)),
                    String.join(Path.SEPARATOR, MainPonderationConcepts.OUT_CONCEPTS_WEIGHTS, timeboxtype));
        }
    }

    /**
     * Writes the best channels for the given timebox and zap aggregation.
     *
     * @param timebox the timebox
     * @param zapAggregation the zap aggregation
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    protected void writeBestChannels(String timebox, TimeboxZapAggregation zapAggregation)
            throws IOException, InterruptedException {
        writeBestChannelsProvidersInOutputFile(aid, timebox, LIVE,
                topLiveChannels.getTopKeys(zapAggregation.getLiveChannelDuration()));
    }

    /**
     * Writes the best providers for the given timebox and zap aggregation.
     *
     * @param timebox the timebox
     * @param zapAggregation the zap aggregation
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    protected void writeBestProviders(String timebox, TimeboxZapAggregation zapAggregation)
            throws IOException, InterruptedException {

        writeBestChannelsProvidersInOutputFile(aid, timebox, VOD,
                topOdProviders.getTopKeys(zapAggregation.getOdProviderDuration()));
    }

    protected void setOutputKey(String key){
        aid = key;
    }

    /**
     * Writes the best channels/providers in the output file for the given aid, timebox, liveOrVod, and bestChannelProviders.
     *
     * @param aid the account ID
     * @param timebox the timebox
     * @param liveOrVod the live or VOD type
     * @param bestChannelProviders the best channel/providers
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    private void writeBestChannelsProvidersInOutputFile(String aid, String timebox, String liveOrVod,
            Map<String, Long> bestChannelProviders)
                    throws IOException, InterruptedException {

        if (!bestChannelProviders.isEmpty()) {
            String timeboxtype = OrangeTimeslotUtils.getTimeboxType(timebox);
            String bestChannelProvidersString = ValuedKeyUtils.parseKeyValueToString(bestChannelProviders);

            String value = String.join(FieldsUtils.TAB, timebox, liveOrVod, bestChannelProvidersString);

            getMosWriter().write(MainPonderationConcepts.OUT_BEST_CHANNELS_PROVIDERS, aid, value,
                    String.join(Path.SEPARATOR,
                            MainPonderationConcepts.OUT_BEST_CHANNELS_PROVIDERS, liveOrVod, timeboxtype));
        }
    }

    /**
     * Set Mos Writer : used in test to use a mock MosWriter
     * @param mos
     */
    public void setMosWriter(MosWriter mos) {
        this.mos = mos;
    }

    /**
     * Get Mos Writer : instantiate it if not set before (production case)
     * @return the Mos writer
     */
    private MosWriter getMosWriter() {
        if (mos == null) {
            mos = new MosWriter();
        }
        return mos;
    }

    /**
     * Closes the Mos writer.
     *
     * @param context the reducer context
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the reducer is interrupted
     */
    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        mos.close();
    }
}
