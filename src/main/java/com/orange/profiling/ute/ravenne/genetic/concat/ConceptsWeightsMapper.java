package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.IOException;

import com.orange.profiling.ute.ravenne.util.AidParser;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.genetic.Counters;
import com.orange.profiling.ute.ravenne.file.ConceptsWeights;
import com.orange.profiling.ute.ravenne.genetic.dao.TimeboxFilter;

/**
 * ConceptsWeightsMapper processes user viewing data to extract concept weights and viewing ratios.
 * 
 * This mapper handles the first input type for the genetic concatenation job:
 * user viewing data that includes concept weights and viewing durations. It:
 * 
 * 1. Processes input records containing:
 *    - User ID (aid)
 *    - Timebox (viewing time period)
 *    - Weighted concepts (user interests)
 *    - Live TV viewing duration
 *    - VOD viewing duration
 * 
 * 2. For each valid record:
 *    - Computes the VOD/Live viewing ratio
 *    - Emits records for both individual users and the opt-out aggregate
 *    - Filters records based on target day processing
 * 
 * Key Features:
 * - Input validation and error counting
 * - Ratio computation between VOD and Live viewing
 * - Support for opt-out aggregation
 * - Time-based filtering of records
 * 
 * Output Format:
 * Key: aid-timebox
 * Value: CW\tconceptWeights\tratioVodLive
 * 
 * <AUTHOR>
 *
 */
public class ConceptsWeightsMapper extends Mapper<Object, Text, Text, Text> {

    private static final double PERCENT = 100.0;

    public static final String CONCEPTS_MARK = "CW";
    public static final int OUT_CONCEPTS_IDX = 1;
    public static final int OUT_RATIO_IDX = 2;
    public static final int OUT_NB_AID = 3;

    private TimeboxFilter timeboxFilter;
    private ConceptsWeights conceptsWeights = new ConceptsWeights();
    private Text aidTimeboxKey = new Text();
    private Text outValue = new Text();
    private static final String TAB = FieldsUtils.TAB;
    private static final String DASH = FieldsUtils.DASH;

    private Text outputKey = new Text();
    private Text outputValue = new Text();

    @Override
    public final void setup(final Context context) {
        String processDay = context.getConfiguration().get(MainConcat.PROCESS_DAY);
        timeboxFilter = new TimeboxFilter(processDay);
    }
    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Mapper#map(KEYIN, VALUEIN,
     * org.apache.hadoop.mapreduce.Mapper.Context)
     */
    @Override
    public final void map(final Object key, final Text value,
            final Context context) throws IOException, InterruptedException {
        conceptsWeights.setValue(value.toString());

        if (conceptsWeights.checkFormat()) {
            String originalAid = conceptsWeights.getField(ConceptsWeights.AID);
            AidParser aidParser = new AidParser(originalAid);

            if (!aidParser.isValidFormat()) {
                context.getCounter(Counters.INVALID_AID_FORMAT).increment(1);
                return;
            }

            String timeslot = conceptsWeights.getField(ConceptsWeights.TIMEBOX);
            String concepts = conceptsWeights.getField(ConceptsWeights.PONDERATED_CONCEPTS);
            Integer liveDuration = Integer.parseInt(conceptsWeights.getField(ConceptsWeights.LIVE_DURATION));
            Integer vodDuration = Integer.parseInt(conceptsWeights.getField(ConceptsWeights.VOD_DURATION));
            Integer totalDuration = liveDuration + vodDuration;

            // Construction de la clé et de la valeur selon le type
            if (aidParser.isStb()) {
                // Traitement STB standard
                outputKey.set(aidParser.getBaseAid() + DASH + timeslot);
                outputValue.set(concepts + TAB + totalDuration);
                context.write(outputKey, outputValue);
            } else {
                // Traitement OTT avec domaine
                String ottTimeslot = timeslot + DASH + aidParser.getDomain();
                outputKey.set(aidParser.getBaseAid() + DASH + ottTimeslot);
                outputValue.set(concepts + TAB + totalDuration);
                context.write(outputKey, outputValue);
            }
        } else {
            context.getCounter(Counters.BAD_FORMAT).increment(1);
        }
    }

    private static int computeRatio(String liveDuration, String vodDuration) {
        int ratio = 0;
        long live = FieldsUtils.getSafelyLong(liveDuration, 0L);
        long vod = FieldsUtils.getSafelyLong(vodDuration, 0L);
        if (live > 0 || vod > 0) {
            ratio = Math.toIntExact(Math.round(PERCENT*vod / (live + vod)));
        }
        return ratio;
    }
}
