package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.IOException;
import java.util.Arrays;
import java.util.stream.Collectors;

import com.orange.profiling.common.utils.FileFilterVodProgbymac;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.joda.time.DateTime;

import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.OrangeTimeslotUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import com.orange.profiling.ute.ravenne.file.Filtered;

/**
 * Reducer implementation for the FilterAndTimeslot component that processes and aggregates
 * viewing data (zaps) from both LIVE TV and VOD sources. This reducer performs duration-based
 * filtering and generates output for both selected views and too-short views that might
 * continue into the next week.
 *
 * <h2>Input Format:</h2>
 * <h3>Key (Tab-separated):</h3>
 * <ol>
 *   <li>aid - Account ID</li>
 *   <li>vodOrLive - View type (VOD or LIVE)</li>
 *   <li>broadcastDuration - Program duration in seconds</li>
 *   <li>providerId - Channel/Provider identifier</li>
 *   <li>contentId - Program/Asset identifier</li>
 *   <li>title - Program title</li>
 *   <li>externalEntertainmentId - External reference ID</li>
 *   <li>seasonName - Season information</li>
 *   <li>seriesName - Series information</li>
 *   <li>offerName - Offer information</li>
 * </ol>
 *
 * <h3>Values (Tab-separated):</h3>
 * <ol>
 *   <li>beginZap - View start timestamp</li>
 *   <li>duration - View duration in seconds</li>
 *   <li>concepts - Program concepts/categories</li>
 * </ol>
 *
 * <h2>Processing Steps:</h2>
 * <ol>
 *   <li>Duration Validation:
 *     <ul>
 *       <li>Ensures durations are between 0 and 5 hours</li>
 *       <li>Handles and corrects anomalous duration values</li>
 *     </ul>
 *   </li>
 *   <li>View Percentage Calculation:
 *     <ul>
 *       <li>Computes view percentage as (totalZapDuration / broadcastDuration) * 100</li>
 *       <li>LIVE threshold: 50% of broadcast duration</li>
 *       <li>VOD threshold: 80% of broadcast duration</li>
 *     </ul>
 *   </li>
 *   <li>View Classification:
 *     <ul>
 *       <li>Selected: Views meeting duration thresholds</li>
 *       <li>Too Short: Views that might continue into next week</li>
 *     </ul>
 *   </li>
 * </ol>
 *
 * <h2>Output:</h2>
 * <h3>1. Selected Views (filtered/):</h3>
 * Format: aid TAB concepts TAB dayOfWeek TAB period TAB timeslot TAB vodLive
 *         TAB zapDuration TAB averageTime TAB providerId TAB contentId TAB title
 *
 * <h3>2. Too Short Views:</h3>
 * Format: aid TAB VOD|LIVE TAB broadcastDuration TAB providerId TAB contentId
 *         TAB title TAB dayOfProgram TAB beginZap TAB zapDuration TAB concepts
 *
 * <h2>Duration Constraints:</h2>
 * <ul>
 *   <li>Maximum Duration: 5 hours ({@value #MAX_DURATION} seconds)</li>
 *   <li>Minimum Duration: 20 minutes ({@value #MIN_DURATION} seconds)</li>
 *   <li>LIVE Threshold: {@value #LIVE_THRESHOLD}% of broadcast duration</li>
 *   <li>VOD Threshold: {@value #VOD_THRESHOLD}% of broadcast duration</li>
 * </ul>
 *
 * @see MainFilterAndTimeslot For the job configuration
 * @see VodMapper For VOD data processing
 * @see ProgbymacMapper For LIVE TV data processing
 */
public class FilterAndTimeslotReducer extends Reducer<Text, Text, Text, Text> {

    private static final int KEY_AID_IDX = 0;
    private static final int KEY_VOD_OR_LIVE_IDX = 1;
    private static final int KEY_BROADCAST_DURATION_IDX = 2;
    private static final int KEY_PROVIDER_ID_IDX = 3;
    private static final int KEY_CONTENT_ID_IDX = 4;
    private static final int KEY_TITLE_IDX = 5;
    private static final int KEY_EXTERNAL_ENTERTAINMENT_ID_IDX = 6;
    private static final int KEY_SEASON_NAME_IDX = 7;
    private static final int KEY_SERIES_NAME_IDX = 8;
    private static final int KEY_OFFER_NAME_IDX = 9;

    private static final int VALUE_LENGTH = 3;
    private static final int VALUE_BEGIN_ZAP_IDX = 0;
    private static final int VALUE_DURATION_IDX = 1;
    private static final int VALUE_CONCEPTS_IDX = 2;

    private static final long PERCENT = 100L;
    private static final long MILLISECONDS = DatesUtils.MILLISECONDS;
    private static final int MAX_DURATION = Math.toIntExact(5*DatesUtils.ONE_HOUR_SECONDS);
    private static final int MIN_DURATION = 1200;

    /**
     * We take into account LIVE program only if seen at least 50% of its duration
     */
    private static final int LIVE_THRESHOLD = 50;
    /**
     * We take into account VOD program only if seen at least 80% of its duration
     */
    private static final int VOD_THRESHOLD = 80;

    private MosWriter mos;
    private Filtered filteredOutput = new Filtered();
    private long minDate = 0;

    private String aid;
    private String vodOrLive;
    private int broadcastDuration;
    private String provider;
    private String contentId;
    private String title;

    private long beginZap;
    private int totalZapDuration;
    private String concepts;
    private String externalEntertainmentId;
    private String seasonName;
    private String seriesName;
    private String offerName;

    /**
     * Initializes the reducer by setting up output writers and configuration parameters.
     * Specifically:
     * <ul>
     *   <li>Opens the MOS writer for output</li>
     *   <li>Retrieves and parses the minimum date from configuration</li>
     * </ul>
     *
     * @param context The Hadoop context containing configuration
     */
    @Override
    public final void setup(final Context context) throws IOException, InterruptedException {
        getMosWriter().open(context);
        String minDateStringFromConf = context.getConfiguration().get(FileFilterVodProgbymac.MIN_DATE);
        DateTime minDateTime = DatesUtils.DF_YYYYMMDD.parseDateTime(minDateStringFromConf);
        minDate = minDateTime.getMillis()/DatesUtils.MILLISECONDS;
    }

    /**
     * Main reduce method that processes viewing records for a given program ID.
     * The method:
     * <ol>
     *   <li>Extracts metadata from the key and values</li>
     *   <li>Validates and adjusts viewing durations</li>
     *   <li>Calculates view percentage</li>
     *   <li>Determines if the view should be:
     *     <ul>
     *       <li>Selected (meets duration threshold)</li>
     *       <li>Preserved for next week (too short but may continue)</li>
     *       <li>Discarded (too short and won't continue)</li>
     *     </ul>
     *   </li>
     * </ol>
     *
     * @param key Program identifier containing metadata
     * @param values Collection of viewing records for this program
     * @param context Hadoop context for output
     */
    @Override
    public final void reduce(final Text key, final Iterable<Text> values,
            final Context context) throws IOException, InterruptedException {

        readKey(key);
        readValues(values);
        tidyDurations();

        long percentView = computePercentView(context);

        // write data
        if (checkPercentViewThreshold(vodOrLive, percentView)) {
            writeViewToOutput(context);
        }
        else if ( shouldKeepViewForNextWeek() ) {
            keepViewForNextWeek(key, context);
        }
    }

    /** Read key to get aid, broadcastDuration and vodOrLive
     *
     * @param key
     */
    private void readKey(final Text key) {
        String[] csvKey = FieldsUtils.TAB_PATTERN.split(key.toString(), -1);

        aid = csvKey[KEY_AID_IDX];
        vodOrLive = csvKey[KEY_VOD_OR_LIVE_IDX];
        broadcastDuration = 0;
        try {
            broadcastDuration = Integer.parseInt(csvKey[KEY_BROADCAST_DURATION_IDX]);
        }
        catch (NumberFormatException e) {
            // Nothing to do : broadcastDuration is 0
        }

        provider = csvKey[KEY_PROVIDER_ID_IDX];
        contentId = csvKey[KEY_CONTENT_ID_IDX];
        title = csvKey[KEY_TITLE_IDX];

        externalEntertainmentId = csvKey[KEY_EXTERNAL_ENTERTAINMENT_ID_IDX];
        seasonName = csvKey[KEY_SEASON_NAME_IDX];
        seriesName = csvKey[KEY_SERIES_NAME_IDX];
        offerName = csvKey[KEY_OFFER_NAME_IDX];
    }

    /** Read values to get beginZap, totalZapDuration and concepts
     *
     * @param values
     */
    private void readValues(final Iterable<Text> values) {
        beginZap = 0;
        totalZapDuration = 0;
        concepts = "";

        // parse values
        // splitted zap aggregation
        for (Text value : values) {

            String[] csv = FieldsUtils.TAB_PATTERN.split(value.toString());

            try {
                long newBeginZap = Long.parseLong(csv[VALUE_BEGIN_ZAP_IDX]);

                if (beginZap == 0 || newBeginZap < beginZap) {
                    beginZap = newBeginZap;
                }

                int zapDuration = Integer.parseInt(csv[VALUE_DURATION_IDX]);
                totalZapDuration += zapDuration;

                // We check length because content could have no concepts or an empty concepts list
                if (csv.length >= VALUE_LENGTH) {
                    concepts = csv[VALUE_CONCEPTS_IDX];
                    concepts = Arrays.stream(concepts.split(",")).filter(l -> !l.contains("/(")).collect(Collectors.joining(","));
                }

            }
            catch (NumberFormatException e) {
                // We don't use this line as beginZap is malformed (not a long)
                // or zap duration is not normal (not an integer)
            }
        }

    }

    /**
     * Validates and adjusts viewing durations to handle anomalous cases.
     * This is necessary because set-top boxes sometimes produce logs with
     * extreme duration values (e.g., 308096151 or -308019759).
     *
     * Constraints applied:
     * <ul>
     *   <li>Maximum duration: 5 hours ({@value #MAX_DURATION} seconds)</li>
     *   <li>Minimum duration: 20 minutes ({@value #MIN_DURATION} seconds)</li>
     *   <li>Both broadcast and zap durations must be positive</li>
     * </ul>
     */
    private void tidyDurations() {
        broadcastDuration = forceBetween(broadcastDuration, 0, MAX_DURATION);
        totalZapDuration = forceBetween(totalZapDuration, 0, MAX_DURATION);
    }

    /** Return the value between min and max.
     * If given value is already between min and max, return the given value.
     * Return max if given value is more than max.
     * Return min if given value is less than min
     */
    private static int forceBetween(int value, int min, int max) {
        int returnValue;
        if (value > max) {
            returnValue = max;
        }
        else if (value < min) {
            returnValue = min;
        }
        else {
            returnValue = value;
        }
        return returnValue;
    }

    /**
     * Calculates the percentage of the program that was viewed.
     * The calculation is: (totalZapDuration / broadcastDuration) * 100
     *
     * Special cases:
     * <ul>
     *   <li>If broadcast duration is 0, percentage is 0</li>
     *   <li>Tracks programs with missing broadcast duration</li>
     * </ul>
     *
     * @param context Hadoop context for counter updates
     * @return Viewing percentage (0-100)
     */
    private long computePercentView(final Context context) {
        long percentView = PERCENT;
        if (broadcastDuration > 0.0) {
            percentView = PERCENT * totalZapDuration / broadcastDuration;
        }
        else {
            context.getCounter(Counters.NB_BROADCAST_ZERO).increment(1);
            if (totalZapDuration< MIN_DURATION) {
                context.getCounter(Counters.NB_BROADCAST_ZERO_SHORTER_20).increment(1);
            }
        }
        return percentView;
    }

    /** Check conditions on the percentView to see if the view can be selected.
     *
     * @param vodOrLive
     * @param percentView
     * @return
     */
    private static boolean checkPercentViewThreshold(String vodOrLive, long percentView) {
        if (TimeboxZapAggregation.LIVE.equals(vodOrLive)) {
            return percentView >= LIVE_THRESHOLD;
        }
        if (TimeboxZapAggregation.VOD.equals(vodOrLive)) {
            return percentView >= VOD_THRESHOLD;
        }
        return false;
    }

    /**
     * Determines if a view should be preserved for processing in the next week.
     * A view is kept if:
     * <ul>
     *   <li>It has non-zero duration</li>
     *   <li>It began in the current week (not previous)</li>
     *   <li>It's either a LIVE or VOD view</li>
     * </ul>
     *
     * This handling is necessary because some viewing sessions span week boundaries.
     *
     * @return true if the view should be kept for next week
     */
    private boolean shouldKeepViewForNextWeek() {
        return (totalZapDuration > 0)
                && ( beginZap > minDate)
                && ( TimeboxZapAggregation.LIVE.equals(vodOrLive) || TimeboxZapAggregation.VOD.equals(vodOrLive) );
    }

    /** If view is selected, writes the view to the output (filtered)
     *
     * @param context
     * @throws IOException
     * @throws InterruptedException
     */
    private void writeViewToOutput(final Context context) throws IOException, InterruptedException {
        long average = beginZap + (broadcastDuration / 2);
        DateTime timeslotTime = new DateTime(average * MILLISECONDS);
        String timeSlot = OrangeTimeslotUtils.getTimeslot(timeslotTime);
        String period = OrangeTimeslotUtils.getPeriod(timeslotTime);
        String dayOfWeek = String.valueOf(timeslotTime.getDayOfWeek());

        mos.write(MainFilterAndTimeslot.OUT_SELECTED, aid,
                filteredOutput.getStringValue(concepts, dayOfWeek, period, timeSlot, vodOrLive,
                        Integer.toString(totalZapDuration), Long.toString(average),
                        provider, contentId, title, externalEntertainmentId, seasonName, seriesName, offerName),
                MainFilterAndTimeslot.OUT_SELECTED + "/part");
        context.getCounter(Counters.NB_PROG_VIEW).increment(1);
    }

    /** This view is too short, but it could be completed by zaps of next week
     *
     * @param key
     * @param context
     * @throws IOException
     * @throws InterruptedException
     */
    private void keepViewForNextWeek(final Text key, final Context context) throws IOException, InterruptedException {

        mos.write(MainFilterAndTimeslot.OUT_TOOSHORT, key.toString(),
                String.join(FieldsUtils.TAB, Long.toString(beginZap),
                        Integer.toString(totalZapDuration), concepts),
                MainFilterAndTimeslot.OUT_TOOSHORT + "/part");

        context.getCounter(Counters.ZAP_TOO_SHORT).increment(1);
    }

    /**
     * Set Mos Writer : used in test to use a mock MosWriter
     * @param mos
     */
    public void setMosWriter(MosWriter mos) {
        this.mos = mos;
    }

    /**
     * Get Mos Writer : instantiate it if not set before (production case)
     * @return
     */
    private MosWriter getMosWriter() {
        if (mos == null) {
            mos = new MosWriter();
        }
        return mos;
    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        mos.close();
    }


}
