package com.orange.profiling.ute.ravenne.genetic.concat;

import java.io.IOException;
import java.util.*;

import com.google.gson.Gson;
import com.orange.profiling.ute.ravenne.genetic.dao.ExclStratLive;
import com.orange.profiling.ute.ravenne.genetic.dao.ExclStratVod;
import com.orange.profiling.ute.ravenne.genetic.dao.ProfileJson;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.joda.time.DateTime;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;

import com.google.gson.FieldNamingPolicy;
import com.google.gson.GsonBuilder;
import com.orange.profiling.common.utils.DatesUtils;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.OrangeTimeslotUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;
import com.orange.profiling.ute.ravenne.genetic.Counters;
import com.orange.profiling.ute.ravenne.util.TopValuedKeySelector;

/**
 * ConcatReducer performs the final aggregation and profile generation for the genetic concatenation job.
 * 
 * This reducer combines all viewing data for each user-timebox combination to create
 * comprehensive viewing profiles. It:
 * 
 * 1. Aggregates and processes:
 *    - Concept weights from content viewing
 *    - VOD/Live viewing ratios
 *    - Top live TV channels
 *    - Top VOD providers
 * 
 * 2. Applies sophisticated processing:
 *    - Selects top N items in each category
 *    - Computes mean values for aggregated data
 *    - Handles different profile types (Live vs VOD)
 *    - Manages profile expiration dates
 * 
 * Key Features:
 * - Configurable limits for top items (concepts, channels, providers)
 * - Different expiration policies for regular and opt-out users
 * - JSON output with customized serialization strategies
 * - Support for both Live and VOD profile types
 * 
 * Configuration Parameters:
 * - topConcepts: Maximum concepts per profile (default: 20)
 * - topLiveChannels: Maximum live channels per profile (default: 5)
 * - topOdProviders: Maximum VOD providers per profile (default: 1000)
 * 
 * Output Format:
 * - Pig V2 compatible format
 * - JSON profile data with expiration and freshness dates
 * - Separate serialization for Live and VOD profiles
 * 
 * <AUTHOR>
 */
public class ConcatReducer extends Reducer<Text, Text, NullWritable, Text> {
    private static final String PIG_KEY_TYPE = "IA" + FieldsUtils.PIPE + "TimeSlots";

    private static final long ONE_DAY_MILLIS = DatesUtils.ONE_DAY_SECONDS * DatesUtils.MILLISECONDS;
    private static final long EXPIRATION_DELAY = 30 * ONE_DAY_MILLIS;
    private static final long EXPIRATION_OPTOUT_DELAY = 100 * ONE_DAY_MILLIS;

    public static final String CONF_TOP_CONCEPTS = "topConcepts";
    public static final String CONF_TOP_LIVE_CHANNELS = "topLiveChannels";
    public static final String CONF_TOP_OD_PROVIDERS = "topOdProviders";

    private static final int DEFAULT_TOP_CONCEPTS = 20;
    private static final int DEFAULT_TOP_LIVE_CHANNELS = 5;
    //Par defaut, on ne veut pas limiter la profondeur de récupération
    //des catchup channel pour les timebox VOD
    private static final int DEFAULT_TOP_OD_PROVIDERS = 1000;

    private long freshnessDate;
    private long expirationDate;
    private long expirationOptoutDate;

    private TopValuedKeySelector topConceptSelector;
    private TopValuedKeySelector topLiveChannelSelector;
    private TopValuedKeySelector topOdProviderSelector;

    private ConcatHelper concatHelper = new ConcatHelper();
    private Text outValue = new Text();
    private ExclStratVod exclStratVOD = new ExclStratVod();
    private ExclStratLive exclStratLive = new ExclStratLive();

    private Gson gsonReplay = new GsonBuilder()
            .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
            .setExclusionStrategies(exclStratVOD).create();
    private Gson gsonLive = new GsonBuilder()
            .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
            .setExclusionStrategies(exclStratLive).create();

    private MultipleOutputs<NullWritable, Text> multipleOutputs;

    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Reducer#setup(org.apache.hadoop.mapreduce
     * .Reducer.Context)
     */
    @Override
    public final void setup(final Context context) {
        setFeshnessAndExpirationDate(context.getConfiguration());
        buidTopSelectors(context.getConfiguration());
        multipleOutputs = new MultipleOutputs<>(context);
    }

    private void setFeshnessAndExpirationDate(final Configuration configuration) {
        String processDateString = configuration.get(MainConcat.PROCESS_DATE);
        DateTime processDate = DatesUtils.DF_YYYYMMDD.parseDateTime(processDateString);
        this.freshnessDate = processDate.getMillis();
        this.expirationDate = freshnessDate + EXPIRATION_DELAY;
        this.expirationOptoutDate = freshnessDate + EXPIRATION_OPTOUT_DELAY;
    }

    private void buidTopSelectors(Configuration configuration) {
        topConceptSelector = new TopValuedKeySelector(
                configuration.getInt(CONF_TOP_CONCEPTS, DEFAULT_TOP_CONCEPTS));

        topLiveChannelSelector = new TopValuedKeySelector(
                configuration.getInt(CONF_TOP_LIVE_CHANNELS, DEFAULT_TOP_LIVE_CHANNELS));

        topOdProviderSelector = new TopValuedKeySelector(
                configuration.getInt(CONF_TOP_OD_PROVIDERS, DEFAULT_TOP_OD_PROVIDERS));
    }

    private boolean isOttTimebox(String timebox) {
        return timebox.endsWith("-Web") || 
               timebox.endsWith("-Mobile") || 
               timebox.endsWith("-SmartTV");
    }

    /*
     * (non-Javadoc)
     *
     * @see org.apache.hadoop.mapreduce.Reducer#reduce(KEYIN, java.lang.Iterable,
     * org.apache.hadoop.mapreduce.Reducer.Context)
     */
    @Override
    public final void reduce(final Text key, final Iterable<Text> values, final Context context)
            throws IOException, InterruptedException {
        concatHelper.initHelper(key);
        concatHelper.aggregateProfilValues(values);

        String timebox = concatHelper.getTimebox();
        String profileJson;
        
        if (isOttTimebox(timebox)) {
            // Traitement spécifique pour OTT
            profileJson = buildOttProfileJsonZapAggregation(timebox);
        } else {
            // Traitement standard STB
            profileJson = buildProfileJsonZapAggregation(timebox);
        }

        writeOutProfileJsonForAidAndTimebox(context, concatHelper.getAid(), timebox, profileJson);
        context.getCounter(Counters.IN_TARGET_DAY).increment(1);
    }

    private String buildProfileJsonZapAggregation(String timebox) {
        ProfileJson profileJson = new ProfileJson();
        setConceptsWeight(profileJson);
        setRatioVodLive(profileJson);
        setLiveChannelDurations(profileJson);
        setOdProviderDurations(profileJson);

        if(timebox.endsWith(OrangeTimeslotUtils.OD_SUFFIX)){
            return gsonReplay.toJson(profileJson);
        }
        else{
            return gsonLive.toJson(profileJson);
        }
    }

    private String buildOttProfileJsonZapAggregation(String timebox) {
        // Logique spécifique pour les profils OTT si nécessaire
        ProfileJson profileJson = new ProfileJson();
        // ... configuration spécifique OTT ...
        return gsonReplay.toJson(profileJson);
    }

    private void setConceptsWeight(ProfileJson profileJson) {
        int nb = concatHelper.getNbValues();

        if (nb>0) {
            TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();
            Map<String, Long> weightedConcepts = zapAggregation.getWeightedConcepts();
            Map<String, Long> topConceptsWeights = topConceptSelector.getTopKeys(weightedConcepts);
            for (Map.Entry<String, Long> conceptWeight : topConceptsWeights.entrySet()) {
                String concept = conceptWeight.getKey();
                String weight = Long.toString(meanValue(conceptWeight.getValue(), nb));
                profileJson.addProfilGenetic(concept, weight);
            }
        }
    }

    private void setRatioVodLive(ProfileJson profileJson) {
        int nb = concatHelper.getNbValues();
        if (nb>0) {
            TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();
            long ratioVodLive = zapAggregation.getLiveDuration();
            int intRatio = Math.toIntExact(meanValue(ratioVodLive, nb));
            profileJson.setRatioVodLive(intRatio);
        }
    }

    private void setLiveChannelDurations(ProfileJson profileJson) {
        int nb = concatHelper.getNbLives();
        if (nb>0) {
            TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();
            Map<String, Long> liveChannelDuration = zapAggregation.getLiveChannelDuration();
            Map<String, Long> topLiveChannels = topLiveChannelSelector.getTopKeys(liveChannelDuration);
            for (String liveChannel : topLiveChannels.keySet()) {
                profileJson.addBestChannel(liveChannel);
            }
        }
    }

    private void setOdProviderDurations(ProfileJson profileJson) {
        int nb = concatHelper.getNbOds();
        if (nb>0) {
            TimeboxZapAggregation zapAggregation = concatHelper.getZapAggregation();
            Map<String, Long> odProviderDuration = zapAggregation.getOdProviderDuration();
            Map<String, Long> topOdProviders = topOdProviderSelector.getTopKeys(odProviderDuration);
            for (String odProvider : topOdProviders.keySet()) {
                profileJson.addBestProvider(odProvider);
            }
        }
    }

    private static long meanValue(long total, int nb) {
        // 1.0 to compute float division
        return Math.round(1.0*total/nb);
    }

    /** Write profile Json for given aid and timebox in pig v2 format
     *
     * @param context
     * @param aid
     * @param timebox
     * @param profileJson
     * @throws IOException
     * @throws InterruptedException
     */
    private void writeOutProfileJsonForAidAndTimebox(final Context context,
            String aid, String timebox, String profileJson)
            throws IOException, InterruptedException {

        String pigKey = String.join(FieldsUtils.SEMICOLON, aid, "", timebox);
        long profilExpirationDate = this.expirationDate;
        if (MainConcat.OPTOUT_AID.equals(aid)) {
            profilExpirationDate = this.expirationOptoutDate;
        }

        String profileString = String.join(FieldsUtils.PIPE,
                pigKey, PIG_KEY_TYPE, profileJson,
                Long.toString(profilExpirationDate), Long.toString(this.freshnessDate));
        outValue.set(profileString);
        context.write(NullWritable.get(), outValue);
    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        multipleOutputs.close();
    }
}
