package com.orange.profiling.ute.ravenne.profiltype.profils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteCountersLogger;

import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Counter;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Logger;

import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.common.utils.FsUtils;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.CatalogueProfilVector;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.ConceptsVector;
import com.orange.profiling.ute.ravenne.profiltype.profils.dao.Profil;

/**
 * Main driver class for the Ravenne Profile Processing System.
 * This class manages the MapReduce job that processes user viewing data (tastebox)
 * and generates user profiles based on concept vectors and profile catalogs.
 * 
 * The system performs the following main tasks:
 * 1. Processes input tastebox data containing user viewing information
 * 2. Matches viewing patterns with predefined concept vectors
 * 3. Generates user profiles based on catalog definitions
 * 4. Outputs profile statistics and vector distributions
 * 
 * Job Configuration:
 * - Input: Tastebox data, Profile catalog, Profile vector catalog, CombiCoeur catalog
 * - Output: User profiles and vector statistics
 * - Processing: Uses TasteboxMapper and ProfilsReducer for data transformation
 * 
 * Statistics Generated:
 * - Number of users per profile type
 * - Vector distribution across profiles
 * - Profile distribution across users
 * 
 * <AUTHOR>
 */
public class MainProfils {

    private static final Logger LOGGER = Logger.getLogger(MainProfils.class);
    
    /** Number of required command line arguments */
    private static final int ARG_LENGTH = 6;
    
    /** Index of input tastebox path in command line arguments */
    private static final int INPUT_TASTEBOX_IDX = 0;
    
    /** Index of profile catalog path in command line arguments */
    private static final int CONF_CATALOG_PROFILS_IDX = 1;
    
    /** Index of profile vector catalog path in command line arguments */
    private static final int CONF_CATALOG_PROFILVECTORS_IDX = 2;
    
    /** Index of CombiCoeur catalog path in command line arguments */
    private static final int CONF_CATALOG_COMBICOEUR_IDX = 3;
    
    /** Index of output directory path in command line arguments */
    private static final int OUTPUT_DIR = 4;

    /** Index of output directory path for vertical scores in command line arguments */
    private static final int OUTPUT_DIR_VERTICAL = 5;

    /** Name of the MapReduce job */
    private static final String JOB_NAME = "ute:ravenne.profils";

    /** Minimum number of fields expected in vector output */
    private static final int OUT_VECTORS_MIN_LENGTH = 3;
    
    /** Index of vector ID in vector output */
    private static final int OUT_VECTORS_VECTOR_IDX = 0;
    
    /** Index of profile ID in vector output */
    private static final int OUT_VECTORS_PROFIL_IDX = 1;
    
    /** Index of count in vector output */
    private static final int OUT_VECTORS_NB_IDX = 2;

    /** Field separator for output files */
    public static final String SEP = FieldsUtils.SEMICOLON;

    /** Configuration key for profile catalog */
    public static final String CATALOG_PROFILS = "catalogProfils";
    
    /** Configuration key for profile vector catalog */
    public static final String CATALOG_PROFILVECTORS = "catalogProfilVectors";
    
    /** Configuration key for CombiCoeur catalog */
    public static final String CATALOG_COMBICOEUR = "combiCoeur";

    /** Configuration key for outputDirVertical  */
    public static final String OUTPUT_VERTICAL = "outputDirVertical";

    /** Counter group for Ravenne-specific metrics */
    public static final String COUNTER_GROUP_RAVENNE = "ravenne";
    
    /** Counter group for number of users per profile */
    public static final String COUNTER_GROUP_NB_USER_BY_PROFILS = "NB_PROFILES_FOYERS";
    
    /** Counter group for profile distribution across users */
    public static final String COUNTER_GROUP_NB_USER_HAVING_N_PROFILS = "NB_FOYERS_HAVING_PROFILE";
    
    /** Counter group for vector distribution per profile */
    public static final String COUNTER_GROUP_NB_VECTOR_BY_PROFIL = "NB_VECTOR_BY_PROFIL";

    /** List of counter groups to be processed */
    private static final String[] COUNTER_GROUPS = {
            COUNTER_GROUP_NB_USER_BY_PROFILS,
            COUNTER_GROUP_RAVENNE,
            COUNTER_GROUP_NB_USER_HAVING_N_PROFILS,
            COUNTER_GROUP_NB_VECTOR_BY_PROFIL
    };

    /** Named output for profile data */
    public static final String OUT_PROFILS = "profils";
    
    /** Named output for vector data */
    public static final String OUT_VECTORS = "vectors";

    /** Named output for score vertical (thematiques) */
    public static final String OUT_SCORES_VERTICALES = "scoresVertical";

    /**
     * Main entry point for the Ravenne Profile Processing job.
     * 
     * Process flow:
     * 1. Validates and processes input arguments
     * 2. Configures and runs the MapReduce job
     * 3. Processes job counters and generates statistics
     * 4. Outputs profile and vector distribution data
     * 
     * Required arguments:
     * - inputPathTastebox: Path to input tastebox data
     * - inputPathCatProfil: Path to profile catalog
     * - inputPathCatProfilVector: Path to profile vector catalog
     * - inputPathCatCombiCoeur: Path to CombiCoeur catalog
     * - outputDir: Output directory path
     *
     * @param args Command line arguments as described above
     * @throws IOException If there are I/O errors during job execution
     * @throws InterruptedException If the job is interrupted
     * @throws ClassNotFoundException If required classes are not found
     * @throws FailedJobException If the MapReduce job fails
     */
    public static void main(final String[] args) throws IOException,
        InterruptedException, ClassNotFoundException, FailedJobException {

        BasicConfigurator.configure();

        // get param
        if (args.length != ARG_LENGTH) {
            String mess = "Takes "
                    + ARG_LENGTH
                    + " arguments : inputPathTastebox "
                    + "inputPathCatProfil inputPathCatProfilVector "
                    + "inputPathCatCombiCoeur outputDir outputDirVertical";

            LOGGER.warn(mess);
            throw new IllegalArgumentException(mess);
        }
        LOGGER.info("Logger initialized");

        // get params
        Path inputPathTastebox = new Path(args[INPUT_TASTEBOX_IDX]);
        String inputPathCatProfil = args[CONF_CATALOG_PROFILS_IDX];
        String inputPathCatProfilVector = args[CONF_CATALOG_PROFILVECTORS_IDX];
        String inputPathCatCombiCoeur = args[CONF_CATALOG_COMBICOEUR_IDX];
        String outputDirName = args[OUTPUT_DIR];
        String outputDirVertical = args[OUTPUT_DIR_VERTICAL];

        // Create configuration
        UteConfiguration conf = new UteConfiguration();
        conf.set(CATALOG_PROFILS, inputPathCatProfil);
        conf.set(CATALOG_PROFILVECTORS, inputPathCatProfilVector);
        conf.set(CATALOG_COMBICOEUR, inputPathCatCombiCoeur);
        // add new output directory, vertical path in configuration for reducer
        conf.set(OUTPUT_VERTICAL, outputDirVertical);


        // Create job
        Job job = Job.getInstance(conf, JOB_NAME);
        job.setJarByClass(MainProfils.class);

        // Setup MapReduce
        job.setMapperClass(TasteboxMapper.class);
        job.setReducerClass(ProfilsReducer.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Input
        MultipleInputs.addInputPath(job, inputPathTastebox,
                TextInputFormat.class, TasteboxMapper.class);

        // Output
        FileOutputFormat.setOutputPath(job, new Path(outputDirName));
        LazyOutputFormat.setOutputFormatClass(job, TextOutputFormat.class);
        MultipleOutputs.addNamedOutput(job, OUT_PROFILS, TextOutputFormat.class, Text.class, Text.class);
        MultipleOutputs.addNamedOutput(job, OUT_VECTORS, TextOutputFormat.class, Text.class, Text.class);
        MultipleOutputs.addNamedOutput(job, OUT_SCORES_VERTICALES, TextOutputFormat.class, Text.class, Text.class);

        // Delete output if exists
        FsUtils fsUtils = new FsUtils(new Path(outputDirName),job.getConfiguration());
        if (fsUtils.pathExists(outputDirName)) {
            fsUtils.deleteFolder(outputDirName);
        }
        // Delete output vertical if exists
        FsUtils fsUtilsVert = new FsUtils(new Path(outputDirVertical),job.getConfiguration());
        if (fsUtilsVert.pathExists(outputDirVertical)) {
            fsUtilsVert.deleteFolder(outputDirVertical);
        }

        // Execute job
        boolean succeeded = job.waitForCompletion(true);
        if (!succeeded) {
            throw new FailedJobException(JOB_NAME);
        }

        UteCountersWriter.writeAlternateCountersAndGroup(job, fsUtils.getFs(),
                new Path(outputDirName), Arrays.asList(COUNTER_GROUPS));


        CatalogueProfilVector catalogueProfil =
                loadCatalogProfilsAndVectors(fsUtils, inputPathCatProfil, inputPathCatProfilVector);

        sendKibanaCounterProfils(COUNTER_GROUP_NB_USER_BY_PROFILS, job, catalogueProfil);
        sendKibanaCounterProfils(COUNTER_GROUP_NB_VECTOR_BY_PROFIL, job, catalogueProfil);
        outputListVectorWithProfilAndNbUser(catalogueProfil, fsUtils, outputDirName);
    }

    /**
     * Loads profile and vector catalogs from the specified input paths.
     * 
     * @param fsUtils File system utilities for HDFS operations
     * @param inputPathCatProfil Path to the profile catalog file
     * @param inputPathCatProfilVector Path to the profile vector catalog file
     * @return Loaded CatalogueProfilVector containing both profiles and vectors
     * @throws IOException If there are errors reading the catalog files
     */
    public static CatalogueProfilVector loadCatalogProfilsAndVectors(FsUtils fsUtils, String inputPathCatProfil,
            String inputPathCatProfilVector) throws IOException {
        CatalogueProfilVector catalogueProfil = new CatalogueProfilVector();
        try (BufferedReader br = fsUtils.getReaderForFile(inputPathCatProfil)) {
            catalogueProfil.loadProfils(br);
        }
        try (BufferedReader br = fsUtils.getReaderForFile(inputPathCatProfilVector)) {
            catalogueProfil.loadProfilsVector(br);
        }
        return catalogueProfil;
    }

    /**
     * Sends profile-related counters to Kibana for monitoring and analysis.
     * Processes job counters and formats them as syslog messages for Kibana ingestion.
     * 
     * @param counterGroup Counter group name to process
     * @param job MapReduce job containing the counters
     * @param catalogueProfil Catalog for profile label lookup
     * @throws IOException If there are errors sending counter data
     */
    private static void sendKibanaCounterProfils(String counterGroup, Job job, CatalogueProfilVector catalogueProfil)
            throws IOException {
        Iterator<Counter> iteCounters = job.getCounters()
                .getGroup(counterGroup).iterator();

        while (iteCounters.hasNext()) {
            Counter counter = iteCounters.next();
            UteCountersLogger logger = new UteCountersLogger();

            String profileId = counter.getName().replace(counterGroup, "");
            String profileLabel = catalogueProfil.getProfil(profileId).labelAndId();

            logger.addType(counterGroup);
            logger.addName(profileLabel);
            logger.addCounter("value", counter.getValue());
            //a log for each counter. to make dashboards dynamically in kibana
            logger.sendJsonLog();
        }
    }

    /**
     * Generates comprehensive vector statistics including unused vectors.
     * This method:
     * 1. Initializes profile and vector count maps
     * 2. Reads and processes vector statistics from job output
     * 3. Generates a consolidated statistics file
     * 
     * @param catalogueProfil Catalog containing profile and vector definitions
     * @param fsUtils File system utilities for HDFS operations
     * @param outputDirName Output directory for statistics file
     * @throws IOException If there are errors reading or writing statistics
     */
    private static void outputListVectorWithProfilAndNbUser(
            CatalogueProfilVector catalogueProfil, FsUtils fsUtils, String outputDirName)
                throws IOException {
        Map<String, Map<String, Integer>> nbUserByProfilAndVector = initProfilAndVector(catalogueProfil);

        readProfilAndVectorStatistics(nbUserByProfilAndVector, fsUtils, outputDirName);

        outputsNbUserByProfilAndVectors(nbUserByProfilAndVector, catalogueProfil, fsUtils, outputDirName);
    }

    /**
     * Initializes a nested map structure for tracking user counts by profile and vector.
     * Creates entries for all possible profile-vector combinations from the catalog.
     * 
     * @param catalogueProfil Catalog containing profile and vector definitions
     * @return Map structure: vector -> (profile -> count)
     */
    private static Map<String, Map<String, Integer>> initProfilAndVector(CatalogueProfilVector catalogueProfil) {
        Map<String,Map<String,Integer>> initProfilAndVector = new HashMap<>();
        for(Profil profil: catalogueProfil.getCatalogue()) {
           for(ConceptsVector vector: profil.getVectors()) {
               Map<String,Integer> nbUserByProfilForThisVector =
                       initProfilAndVector.getOrDefault(vector.toString(), new HashMap<>());
               nbUserByProfilForThisVector.put(profil.getProfilId(), 0);
               initProfilAndVector.put(vector.toString(), nbUserByProfilForThisVector);
           }
        }
        return initProfilAndVector;
    }

    /**
     * Reads and processes vector statistics from job output files.
     * Aggregates user counts for each profile-vector combination.
     * 
     * @param nbUserByProfilAndVector Map to store aggregated statistics
     * @param fsUtils File system utilities for HDFS operations
     * @param outputDirName Directory containing vector statistics files
     * @throws IOException If there are errors reading statistics files
     */
    private static void readProfilAndVectorStatistics(
            Map<String, Map<String, Integer>> nbUserByProfilAndVector,
            FsUtils fsUtils, String outputDirName) throws IOException {

        String inputStatsVectorsDir = outputDirName+"/vectors";
        RemoteIterator<LocatedFileStatus> inputStatsVectorFiles =
                fsUtils.getFs().listFiles(new Path(inputStatsVectorsDir), false);

        while(inputStatsVectorFiles.hasNext()) {
            String inputVectorsFileName = inputStatsVectorsDir+"/"+inputStatsVectorFiles.next().getPath().getName();
            try (BufferedReader inputVectorsFile = fsUtils.getReaderForFile(inputVectorsFileName)) {
                String line = inputVectorsFile.readLine();
                while(line != null) {
                    addProfilAndVectorStatistic(nbUserByProfilAndVector, line);
                    line = inputVectorsFile.readLine();
                }
            }

        }
    }

    /**
     * Processes a single line of vector statistics and updates count maps.
     * Expected format: vector_id<tab>profile_id<tab>count
     * 
     * @param nbUserByProfilAndVector Map to update with statistics
     * @param line Input line containing vector statistics
     */
    private static void addProfilAndVectorStatistic(
            Map<String, Map<String, Integer>> nbUserByProfilAndVector, String line) {

        String[] parts = FieldsUtils.TAB_PATTERN.split(line.trim());
        if (parts.length >= OUT_VECTORS_MIN_LENGTH) {
            String vector = parts[OUT_VECTORS_VECTOR_IDX];
            String profilId = parts[OUT_VECTORS_PROFIL_IDX];
            Integer nb = 0;
            try {
                nb = Integer.parseInt(parts[OUT_VECTORS_NB_IDX]);
            }
            catch(NumberFormatException e) {
                // nothing to do, nb is 0 if it can't be parsed as integer
            }
            Map<String,Integer> nbUserByProfilForThisVector =
                    nbUserByProfilAndVector.getOrDefault(vector, new HashMap<>());
            nbUserByProfilForThisVector.merge(profilId, nb, Integer::sum);
            nbUserByProfilAndVector.put(vector, nbUserByProfilForThisVector);
        }
    }

    /**
     * Writes consolidated vector statistics to a CSV file.
     * Output format: vector;profile_id;profile_label;count
     * 
     * @param nbUserByProfilAndVector Map containing aggregated statistics
     * @param catalogueProfil Catalog for profile label lookup
     * @param fsUtils File system utilities for HDFS operations
     * @param outputDirName Output directory for statistics file
     * @throws IOException If there are errors writing the statistics file
     */
    private static void outputsNbUserByProfilAndVectors(Map<String, Map<String, Integer>> nbUserByProfilAndVector,
            CatalogueProfilVector catalogueProfil, FsUtils fsUtils, String outputDirName) throws IOException {

        String outputStasVector = outputDirName+"/"+"statsVectors.csv";
        try (BufferedWriter bw = fsUtils.getWriterForFile(outputStasVector)) {
            for(Map.Entry<String, Map<String,Integer>> entryVector : nbUserByProfilAndVector.entrySet()) {
                String vector = entryVector.getKey();
                for(Map.Entry<String,Integer> entryProfil: entryVector.getValue().entrySet()) {
                    String profilId = entryProfil.getKey();
                    Profil profil = catalogueProfil.getProfil(profilId);
                    Integer nb = entryProfil.getValue();
                    bw.write(vector+SEP+profil.getProfilId()+SEP+profil.getLabel()+SEP+nb+"\n");
                }
            }
        }
    }
}
