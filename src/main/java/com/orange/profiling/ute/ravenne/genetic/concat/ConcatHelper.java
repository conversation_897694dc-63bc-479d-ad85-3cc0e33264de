package com.orange.profiling.ute.ravenne.genetic.concat;

import org.apache.hadoop.io.Text;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

/**
 * ConcatHelper provides utility functions for aggregating and managing profile data.
 * 
 * This helper class encapsulates common functionality used by both the ConcatCombiner
 * and ConcatReducer for processing user viewing profiles. It:
 * 
 * 1. Manages profile data:
 *    - Parses aid-timebox keys
 *    - Tracks record counts by type
 *    - Maintains aggregation state
 * 
 * 2. Handles aggregation of:
 *    - Concept weights and ratios
 *    - Live channel viewing durations
 *    - VOD provider viewing durations
 * 
 * Key Features:
 * - Stateful processing of profile data
 * - Support for multiple input types
 * - Count tracking by data category
 * - Clean separation of aggregation logic
 * 
 * Usage:
 * 1. Initialize with initHelper()
 * 2. Process values with aggregateProfilValues()
 * 3. Access results through getter methods
 * 
 * <AUTHOR>
 */
public class ConcatHelper {
    private static final int KEY_AID_IDX = 0;
    private static final int KEY_TIMEBOX_IDX = 1;


    private String aid;
    private String timebox;
    private int nbValues;
    private int nbLives;
    private int nbOds;
    private TimeboxZapAggregation zapAggregation;

    public ConcatHelper() {};

    public void initHelper(Text key) {
        String[] keyParts = FieldsUtils.DASH_PATTERN.split(key.toString());
        aid = keyParts[KEY_AID_IDX];
        timebox = keyParts[KEY_TIMEBOX_IDX];

        nbValues = 0;
        nbLives = 0;
        nbOds = 0;
    }

    public TimeboxZapAggregation aggregateProfilValues(final Iterable<Text> values) {
        zapAggregation = new TimeboxZapAggregation(timebox);
        for(Text value : values) {
            String[] csv = FieldsUtils.TAB_PATTERN.split(value.toString());
            String mark = csv[0];
            if (ConceptsWeightsMapper.CONCEPTS_MARK.equals(mark)) {
                aggregateConceptsAndRatio(zapAggregation, csv);
            }
            else if (TimeboxZapAggregation.LIVE.equals(mark)) {
                aggregateLiveChannelDurations(zapAggregation, csv);
            }
            else if (TimeboxZapAggregation.VOD.equals(mark)) {
                aggregateOdProviderDurations(zapAggregation, csv);
            }
        }
        return zapAggregation;
    }


    private void aggregateConceptsAndRatio(TimeboxZapAggregation zapAggregation, String[] csv) {
        String ponderatedConcepts = csv[ConceptsWeightsMapper.OUT_CONCEPTS_IDX];
        long ratioVodLive = FieldsUtils.getSafelyLong(
                csv[ConceptsWeightsMapper.OUT_RATIO_IDX], 0L);
        zapAggregation.addConceptWeights(ponderatedConcepts);
        zapAggregation.addDuration(TimeboxZapAggregation.LIVE, ratioVodLive);
        nbValues += getNbFieldValue(csv, ConceptsWeightsMapper.OUT_NB_AID);
    }

    private void aggregateLiveChannelDurations(TimeboxZapAggregation zapAggregation, String[] csv) {
        String liveChannelsDurations = csv[BestChannelsProvidersMapper.OUT_DURATIONS_IDX];
        zapAggregation.addLiveChannelDuration(liveChannelsDurations);
        nbLives += getNbFieldValue(csv, BestChannelsProvidersMapper.OUT_NB_AID);
    }

    private void aggregateOdProviderDurations(TimeboxZapAggregation zapAggregation, String[] csv) {
        String odProvidersDurations = csv[BestChannelsProvidersMapper.OUT_DURATIONS_IDX];
        zapAggregation.addOdProviderDuration(odProvidersDurations);
        nbOds += getNbFieldValue(csv, BestChannelsProvidersMapper.OUT_NB_AID);
    }

    private static int getNbFieldValue(String[] csv, int index) {
        int nb = 1;
        if (csv.length > index) {
            nb = FieldsUtils.getSafelyInteger(csv[index], 1);
        }
        return nb;
    }

    /**
     * @return the aid
     */
    public String getAid() {
        return aid;
    }

    /**
     * @return the timebox
     */
    public String getTimebox() {
        return timebox;
    }

    /**
     * @return the nbValues
     */
    public int getNbValues() {
        return nbValues;
    }

    /**
     * @return the nbLives
     */
    public int getNbLives() {
        return nbLives;
    }

    /**
     * @return the nbOds
     */
    public int getNbOds() {
        return nbOds;
    }

    /**
     * @return the zapAggregation
     */
    public TimeboxZapAggregation getZapAggregation() {
        return zapAggregation;
    }
}