package com.orange.profiling.ute.ravenne.util;

import java.util.Collections;
import java.util.Map;
import java.util.HashMap;

/**
 * Utility class for parsing and validating user identifiers in both STB and OTT formats.
 * 
 * Supported formats:
 * - STB format: {baseAid}-STB
 * - OTT formats: 
 *   * {baseAid}-Web
 *   * {baseAid}-Mobile
 *   * {baseAid}-SmartTV
 * 
 * Example usage:
 * <pre>
 * AidParser parser = new AidParser("ABC123-Web");
 * if (parser.isOtt()) {
 *     String domain = parser.getDomain(); // Returns "Web"
 *     String baseAid = parser.getBaseAid(); // Returns "ABC123"
 * }
 * </pre>
 */
public class AidParser {
    private static final String STB_SUFFIX = "-STB";
    private static final Map<String, String> DOMAIN_SUFFIXES = Collections.unmodifiableMap(new HashMap<String, String>() {{
        put("-Web", "Web");
        put("-Mobile", "Mobile");
        put("-SmartTV", "SmartTV");
    }});

    private final String baseAid;
    private final String domain;
    private final boolean isStb;

    public AidParser(String aid) {
        if (aid.endsWith(STB_SUFFIX)) {
            this.baseAid = aid.substring(0, aid.length() - STB_SUFFIX.length());
            this.domain = null;
            this.isStb = true;
        } else {
            this.isStb = false;
            this.domain = DOMAIN_SUFFIXES.entrySet().stream()
                .filter(entry -> aid.endsWith(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);
            this.baseAid = domain != null ? 
                aid.substring(0, aid.length() - (domain.length() + 1)) : aid;
        }
    }

    public String getBaseAid() { return baseAid; }
    public String getDomain() { return domain; }
    public boolean isStb() { return isStb; }
    public boolean isOtt() { return domain != null; }
    public boolean isValidFormat() { return isStb || isOtt(); }
}