package com.orange.profiling.ute.ravenne.genetic.concat;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.mapred.UteConfiguration;
import com.orange.profiling.common.mapred.UteCountersWriter;
import com.orange.profiling.common.utils.DatesUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.MultipleInputs;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.log4j.BasicConfigurator;
import org.joda.time.DateTime;
import java.io.IOException;
import com.orange.profiling.common.utils.FsUtils;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.io.NullWritable;

/**
 * MainConcat est la classe principale du job MapReduce de Concaténation Génétique.
 * 
 * Ce job combine trois types de données d'entrée :
 * 1. Poids des concepts - Préférences de contenu et habitudes de visionnage
 * 2. Meilleures chaînes - Chaînes TV en direct les plus regardées
 * 3. Meilleurs fournisseurs - Fournisseurs VOD (Vidéo à la Demande) les plus utilisés
 * 
 * Le job traite ces entrées pour créer des profils utilisateur complets incluant :
 * - Concepts pondérés représentant les centres d'intérêt
 * - Ratio entre VOD et TV en direct
 * - Chaînes en direct les plus regardées
 * - Fournisseurs VOD les plus utilisés
 * 
 * Fonctionnalités principales :
 * - Gestion de plusieurs formats d'entrée via des mappers distincts
 * - Sélection configurable du top N pour concepts, chaînes et fournisseurs
 * - Traitement spécifique des utilisateurs désinscrits
 * - Gestion des dates d'expiration des profils
 * - Format de sortie compatible avec les traitements en aval
 * 
 * Paramètres de configuration :
 * - topConcepts : Nombre maximum de concepts à conserver par profil
 * - topLiveChannels : Nombre maximum de chaînes en direct à conserver
 * - topOdProviders : Nombre maximum de fournisseurs VOD à conserver
 * 
 * <AUTHOR>
 */
public class MainConcat {
    private static final Log LOG = LogFactory.getLog(MainConcat.class);

    private static final int ARG_LENGTH = 6; // Minimum required arguments
    private static final int INPUT_CONCEPTS_WEIGHTS_IDX = 0;
    private static final int INPUT_BEST_CHANNELS_IDX = 1;
    private static final int INPUT_BEST_PROVIDERS_IDX = 2;
    private static final int PROCESS_DATE_IDX = 3;
    private static final int OUTPUT_STB_DIR_IDX = 4;
    private static final int OUTPUT_OTT_DIR_IDX = 5;
    private static final int TOP_CONCEPTS_IDX = 6;
    private static final int TOP_LIVE_CHANNELS_IDX = 7;
    private static final int TOP_OD_PROVIDERS_IDX = 8;

    private static final String JOB_NAME = "ute.ravenne.genetic.concat";
    
    public static final String PROCESS_DATE = "processDate";
    public static final String PROCESS_DAY = "processDay";

    public static final String OPTOUT_AID = "999999999";
    
    private static final String OUTPUT_STB = "OUTPUT_STB";
    private static final String OUTPUT_OTT = "OUTPUT_OTT";

    public static void main(final String[] args) throws IOException, ClassNotFoundException, InterruptedException, FailedJobException {
        BasicConfigurator.configure();

        if (args.length < ARG_LENGTH) {
            String mess = "Takes " + ARG_LENGTH + " arguments : "
                    + "inputConceptsWeights inputBestChannels inputBestProviders "
                    + "processDate outputStbDir outputOttDir "
                    + "[topConcepts topLiveChannels topOdProviders]";
            LOG.error(mess);
            throw new IllegalArgumentException(mess);
        }

        // get params
        Path inputPathConceptsWeights = new Path(args[INPUT_CONCEPTS_WEIGHTS_IDX]);
        Path inputPathBestChannels = new Path(args[INPUT_BEST_CHANNELS_IDX]);
        Path inputPathBestProviders = new Path(args[INPUT_BEST_PROVIDERS_IDX]);
        String processDate = args[PROCESS_DATE_IDX];
        Path outputStbDir = new Path(args[OUTPUT_STB_DIR_IDX]);
        Path outputOttDir = new Path(args[OUTPUT_OTT_DIR_IDX]);

        // Create configuration
        UteConfiguration conf = new UteConfiguration();
        conf.set(PROCESS_DATE, processDate);
        conf.set(PROCESS_DAY, getProcessDay(processDate));
        conf.set(OUTPUT_STB, outputStbDir.toString());
        conf.set(OUTPUT_OTT, outputOttDir.toString());

        // Set optional top parameters if provided
        setOptionalTopConf(conf, ConcatReducer.CONF_TOP_CONCEPTS, TOP_CONCEPTS_IDX, args);
        setOptionalTopConf(conf, ConcatReducer.CONF_TOP_LIVE_CHANNELS, TOP_LIVE_CHANNELS_IDX, args);
        setOptionalTopConf(conf, ConcatReducer.CONF_TOP_OD_PROVIDERS, TOP_OD_PROVIDERS_IDX, args);

        // Configure job
        Job job = Job.getInstance(conf, JOB_NAME);
        job.setJarByClass(MainConcat.class);
        job.setNumReduceTasks(200);

        // Setup MapReduce
        job.setMapperClass(ConceptsWeightsMapper.class);
        job.setCombinerClass(ConcatCombiner.class);
        job.setReducerClass(ConcatReducer.class);

        // Specify key / value
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Input
        MultipleInputs.addInputPath(job, inputPathConceptsWeights, TextInputFormat.class,
                        ConceptsWeightsMapper.class);
        MultipleInputs.addInputPath(job, inputPathBestchannels, TextInputFormat.class,
                        BestChannelsProvidersMapper.class);
        MultipleInputs.addInputPath(job, inputPathBestProviders, TextInputFormat.class,
                        BestChannelsProvidersMapper.class);

        // Output
        FileOutputFormat.setOutputPath(job, outputStbDir);
        configureMultipleOutputs(job);

        // Execute job
        if (!job.waitForCompletion(true)) {
            throw new FailedJobException(JOB_NAME);
        }
    }

    private static String getProcessDay(String processDate) {
        DateTime processDateTime = DatesUtils.DF_YYYYMMDD.parseDateTime(processDate);
        return DatesUtils.DF_DAY_OF_WEEK.print(processDateTime.plusDays(1));
    }

    private static void setOptionalTopConf(UteConfiguration conf, String key, int argIdx, String[] args) {
        if (args.length > argIdx) {
            conf.set(key, args[argIdx]);
        }
    }
}
