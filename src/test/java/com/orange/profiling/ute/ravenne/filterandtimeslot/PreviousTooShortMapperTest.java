package com.orange.profiling.ute.ravenne.filterandtimeslot;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;

import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;

public class PreviousTooShortMapperTest {

    MapDriver<Object, Text, Text, Text> ptsMapDriver;

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {

        PreviousTooShortMapper ptsMapper = new PreviousTooShortMapper();

        ptsMapDriver = MapDriver.newMapDriver(ptsMapper);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public void testLiveOk() throws IOException {
        String aid = "123456789";
        String vodOrLive = "LIVE";
        String showDuration = "7200";
        String provider = "TF1";
        String contentId = "1234";
        String title = "La vie secrete des doryphore";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";
        String dayOfProgram = "20190712";
        String beginZap = "**********";
        String zapDuration = "123";
        String concepts = "categorie/serie,ambiance et ton/insomnie,sujets/patate";

        ptsMapDriver.withInput(new LongWritable(), new Text(
                String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration,
                        provider, contentId, title,
                        externalEntertainmentId, seasonName, seriesName, offerName,
                        dayOfProgram, beginZap, zapDuration, concepts))
            );

        ptsMapDriver.withOutput(
                new Text(String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration, provider, contentId, title,
                        externalEntertainmentId, seasonName, seriesName, offerName, dayOfProgram)),
                new Text(String.join(FieldsUtils.TAB,  beginZap, zapDuration, concepts))
            );
        ptsMapDriver.runTest();

    }

    @Test
    public void testLiveOkEmptyConcepts() throws IOException {
        String aid = "123456789";
        String vodOrLive = "LIVE";
        String showDuration = "7200";
        String provider = "TF1";
        String contentId = "1234";
        String title = "La vie secrete des doryphore";
        String offerName = "LIVE";
        String dayOfProgram = "20190712";
        String beginZap = "**********";
        String zapDuration = "123";
        String concepts = "";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";

        ptsMapDriver.withInput(new LongWritable(), new Text(
                String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration,
                        provider, contentId, title, externalEntertainmentId, seasonName, seriesName, offerName,
                        dayOfProgram, beginZap, zapDuration, concepts))
            );

        ptsMapDriver.withOutput(
                new Text(String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration, provider, contentId, title,
                         externalEntertainmentId, seasonName, seriesName , offerName, dayOfProgram)),
                new Text(String.join(FieldsUtils.TAB,  beginZap, zapDuration, concepts))
            );
        ptsMapDriver.runTest();

    }

    @Test
    public void testLiveTooShort() throws IOException {
        String aid = "123456789";
        String vodOrLive = "LIVE";
        String showDuration = "7200";
        String provider = "TF1";
        String contentId = "1234";
        String title = "La vie secrete des doryphore";
        String dayOfProgram = "20190712";
        String beginZap = "**********";
        String zapDuration = "123";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "LIVE";

        ptsMapDriver.withInput(new LongWritable(), new Text(
                String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration,
                        provider, contentId, title
                        , externalEntertainmentId, seasonName, seriesName, offerName
                        , dayOfProgram, beginZap, zapDuration))
            );

        ptsMapDriver.runTest();
    }

    @Test
    public void testVodOk() throws IOException {
        String aid = "123456789";
        String vodOrLive = "VOD";
        String showDuration = "4800";
        String provider = "TF1";
        String contentId = "SVOD1234";
        String title = "Dernier train pour Busan";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "TVOD";
        String dayOfProgram = "0";
        String beginZap = "**********";
        String zapDuration = "123";
        String concepts = "categorie/fiction,ambiance et ton/horreur,personnages/zombies";


        ptsMapDriver.withInput(new LongWritable(), new Text(
                String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration,
                        provider, contentId, title, externalEntertainmentId, seasonName, seriesName, offerName
                        , dayOfProgram, beginZap, zapDuration, concepts))
            );

        ptsMapDriver.withOutput(
                new Text(String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration, provider, contentId, title
                        , externalEntertainmentId, seasonName, seriesName, offerName, dayOfProgram)),
                new Text(String.join(FieldsUtils.TAB,  beginZap, zapDuration, concepts))
            );
        ptsMapDriver.runTest();

    }

    @Test
    public void testVodOkEmptyConcepts() throws IOException {
        String aid = "123456789";
        String vodOrLive = "VOD";
        String showDuration = "4800";
        String provider = "TF1|catchuptv_xxx";
        String contentId = "SVOD1234";
        String title = "Dernier train pour Busan";
        String dayOfProgram = "0";
        String beginZap = "**********";
        String zapDuration = "123";
        String concepts = "";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "TVOD";

        ptsMapDriver.withInput(new LongWritable(), new Text(
                String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration,
                        provider, contentId, title
                        , externalEntertainmentId, seasonName, seriesName, offerName
                        , dayOfProgram, beginZap, zapDuration, concepts))
            );

        ptsMapDriver.withOutput(
                new Text(String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration, provider, contentId, title
                        , externalEntertainmentId, seasonName, seriesName, offerName, dayOfProgram)),
                new Text(String.join(FieldsUtils.TAB,  beginZap, zapDuration, concepts))
            );
        ptsMapDriver.runTest();

    }

    @Test
    public void testVodTooShort() throws IOException {
        String aid = "123456789";
        String vodOrLive = "VOD";
        String showDuration = "4800";
        String provider = "TF1";
        String contentId = "SVOD1234";
        String title = "Dernier train pour Busan";
        String dayOfProgram = "0";
        String beginZap = "**********";
        String zapDuration = "123";
        String externalEntertainmentId = "episode-Title of serie and episode-season-X-episode-Y-2000";
        String seasonName = "Saison X";
        String seriesName = "Title of serie";
        String offerName = "TVOD";

        ptsMapDriver.withInput(new LongWritable(), new Text(
                String.join(FieldsUtils.TAB,  aid, vodOrLive, showDuration,
                        provider, contentId, title
                        , externalEntertainmentId, seasonName, seriesName, offerName
                        , dayOfProgram, beginZap, zapDuration))
            );

        ptsMapDriver.runTest();
    }
}
