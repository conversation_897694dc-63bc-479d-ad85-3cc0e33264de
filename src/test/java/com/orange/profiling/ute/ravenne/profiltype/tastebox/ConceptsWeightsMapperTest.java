package com.orange.profiling.ute.ravenne.profiltype.tastebox;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.MapDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;

/**
 * <AUTHOR>
 */
public class ConceptsWeightsMapperTest {
    private static final String TAB = FieldsUtils.TAB;

    MapDriver<Object, Text, Text, Text> mapperDriver;

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() {
        ConceptsWeightsMapper conceptsWeightsMapper = new ConceptsWeightsMapper();

        mapperDriver = MapDriver.newMapDriver(conceptsWeightsMapper);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testConceptsWeightsMapper() throws IOException {
        String aid = "123456789";
        String timeslot = "d2t3";
        String concepts = "sujets/sujets=500,sujets/sport=750,sujets/rugby=1000";
        String liveDuration = "2400";
        String vodDuration = "1400";
        String totalDuration = "3800";

        mapperDriver.withInput(new LongWritable(),
                new Text(aid+TAB+timeslot+TAB+concepts+TAB+liveDuration+TAB+vodDuration));

        mapperDriver.withOutput(new Text(aid+"-"+timeslot),
                new Text(concepts+TAB+totalDuration));

        mapperDriver.runTest();
    }

    @Test
    public final void testConceptsWeightsMapperBadLength() throws IOException {
        String aid = "123456789";
        String timeslot = "d2t3";
        String concepts = "sujets/sujets=500,sujets/sport=750,sujets/rugby=1000";
        String liveDuration = "2400";

        mapperDriver.withInput(new LongWritable(),
                new Text(aid+TAB+timeslot+TAB+concepts+TAB+liveDuration));

        mapperDriver.withCounter(Counters.BAD_FORMAT, 1);

        mapperDriver.runTest();
    }

    @Test
    public void testStbMapping() {
        String input = "ABC123-STB sujets/sport,sujets/football 5 10 5 LIVE 300";
        mapperDriver.withInput(new Object(), new Text(input))
                .withOutput(new Text("ABC123-5"), new Text("sujets/sport,sujets/football\t15"))
                .runTest();
    }

    @Test
    public void testOttWebMapping() {
        String input = "ABC123-Web sujets/sport,sujets/football 5 10 5 LIVE 300";
        mapperDriver.withInput(new Object(), new Text(input))
                .withOutput(new Text("ABC123-5-Web"), new Text("sujets/sport,sujets/football\t15"))
                .runTest();
    }

    @Test
    public void testInvalidAidFormat() {
        String input = "ABC123-Invalid sujets/sport 5 10 5 LIVE 300";
        mapperDriver.withInput(new Object(), new Text(input))
                .runTest();
        assertEquals(1L, mapperDriver.getCounters()
                .findCounter(Counters.INVALID_AID_FORMAT).getValue());
    }
}
