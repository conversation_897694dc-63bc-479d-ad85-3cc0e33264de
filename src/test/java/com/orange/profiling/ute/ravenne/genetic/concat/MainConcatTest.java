package com.orange.profiling.ute.ravenne.genetic.concat;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;


/**
 * <AUTHOR>
 *
 */
public class MainConcatTest {
    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        System.setProperty("hadoop.home.dir", "/");
        runtimeRessourceFolder = MainConcatTest.class.getResource("/ute-ravenne-concat/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMainConcatKo()
            throws ClassNotFoundException, FailedJobException, InterruptedException, IOException {

        String[] args = { "notgood" };
        try {
            MainConcat.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (IllegalArgumentException e) {
            assertTrue("IllegalArgumentException has been thrown", true);
        }
    }

    @Test
    public final void testMainConcatOk2() throws IOException, InterruptedException, ClassNotFoundException, FailedJobException {
        String inputRavenneGenetic = runtimeRessourceFolder.resolve("in2/2020/27/conceptsweights").getPath();
        String inputBestChannels = runtimeRessourceFolder.resolve("in2/2020/27/bestchannelsproviders/LIVE").getPath() + "/part*";
        String inputBestProviders = runtimeRessourceFolder.resolve("in2/2020/27/bestchannelsproviders/VOD").getPath() + "/part*";
        String outputDir = runtimeRessourceFolder.resolve("output2").getPath();

        String[] args = {
                inputRavenneGenetic,
                inputBestChannels,
                inputBestProviders,
                "20200706",
                outputDir,
                "20", "5", "5"
        };

        MainConcat.main(args);
    }


    @Test
    public final void testMainConcatOk() throws IOException, InterruptedException, ClassNotFoundException, FailedJobException {
        String inputRavenneGenetic = runtimeRessourceFolder.resolve("in/2020/12/conceptsweights").getPath();
        String inputBestChannels = runtimeRessourceFolder.resolve("in/2020/12/bestchannelsproviders/LIVE").getPath() + "/part*";
        String inputBestProviders = runtimeRessourceFolder.resolve("in/2020/12/bestchannelsproviders/VOD").getPath() + "/part*";
        String outputDir = runtimeRessourceFolder.resolve("output").getPath();

        String[] args = {
                inputRavenneGenetic,
                inputBestChannels,
                inputBestProviders,
                "20200316",
                outputDir,
                "20", "5", "5"
        };

        MainConcat.main(args);

        String expectedDir = runtimeRessourceFolder.resolve("expected").getPath();
        List<String> expectedCounters = TestUtils.getLinesFromPath(expectedDir, "_COUNTERS");
        List<String> actualCounters = TestUtils.getLinesFromPath(outputDir, "_COUNTERS");
        assertEquals("Counters ", expectedCounters, actualCounters);

        List<String> expectedSelected = TestUtils.getLinesFromPath(expectedDir, "part-concat");
        List<String> actualSelected = TestUtils.getLinesFromPath(outputDir, "part*");
        assertEquals("Concat", expectedSelected, actualSelected);

    }

    @Test
    public final void testMainConcatOkNothprov() throws IOException, InterruptedException, ClassNotFoundException, FailedJobException {
        String inputRavenneGenetic = runtimeRessourceFolder.resolve("in/2020/12/conceptsweights").getPath();
        String inputBestChannels = runtimeRessourceFolder.resolve("in/2020/12/bestchannelsproviders/LIVE").getPath() + "/part*";
        String inputBestProviders = runtimeRessourceFolder.resolve("in/2020/12/bestchannelsproviders/VOD").getPath() + "/part*";
        String outputDir = runtimeRessourceFolder.resolve("outputnothprov").getPath();
        String flagFile = runtimeRessourceFolder.resolve("in/flagfilewithexport0").getPath();

        String[] args = {
                inputRavenneGenetic,
                inputBestChannels,
                inputBestProviders,
                "20200316",
                outputDir,
                "20", "5", "5",
                flagFile
        };

        MainConcat.main(args);

        String expectedDir = runtimeRessourceFolder.resolve("expectednothprov").getPath();

        List<String> expectedCounters = TestUtils.getLinesFromPath(expectedDir, "_COUNTERS");
        List<String> actualCounters = TestUtils.getLinesFromPath(outputDir, "_COUNTERS");
        assertEquals("Counters ", expectedCounters, actualCounters);

        List<String> expectedSelected = TestUtils.getLinesFromPath(expectedDir, "part-concat");
        List<String> actualSelected = TestUtils.getLinesFromPath(outputDir, "part*");
        assertEquals("Concat", expectedSelected, actualSelected);

    }
}