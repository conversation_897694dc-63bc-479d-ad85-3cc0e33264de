import org.apache.hadoop.mapreduce.Job;
import org.junit.Test;

public class MainConcatIntegrationTest {
    
    @Test
    public void testCompleteFlow() throws Exception {
        // Setup test data
        String[] testInputs = {
            "ABC123-STB sujets/sport 5 10 5 LIVE 300",
            "ABC123-Web sujets/sport 5 10 5 LIVE 300"
        };
        
        // Configure and run job
        Job job = MainConcat.configureJob(/* test configuration */);
        
        // Verify outputs
        // Assert STB and OTT outputs are correctly generated
    }
}