package com.orange.profiling.ute.ravenne.profiltype.profils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.exceptions.FailedJobException;
import com.orange.profiling.common.utils.unit_tests.TestUtils;

/**
 * <AUTHOR>
 */
public class MainProfilsTest {
    private URI runtimeRessourceFolder;

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = MainProfilsTest.class.getResource("/ute-ravenne-profils/").toURI();

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public final void testMainKo() {

        String[] args = new String[1];
        args[0] = "";
        try {
            MainProfils.main(args);
            fail("Should have thrown an IllegalArgumentException because no parameters");
        }
        catch (Exception e) {
            assertEquals(
                    "Takes 6 arguments : inputPathTastebox inputPathCatProfil inputPathCatProfilVector inputPathCatCombiCoeur outputDir outputDirVertical",
                    e.getMessage());
        }
    }

    @Test
    public final void testMainOk()
            throws IOException, FailedJobException, InterruptedException, ClassNotFoundException, URISyntaxException {

        String inputPathTastebox = runtimeRessourceFolder.resolve("in/aid").getPath();
        String inputPathCatProfil = runtimeRessourceFolder.resolve("in/catalogue/catalog_profils.csv").getPath();
        String inputPathCatProfilVector = runtimeRessourceFolder.resolve("in/catalogue/catalog_profils_vecteur.json").getPath();
        String inputPathCatCombiCoeur = runtimeRessourceFolder.resolve("in/catalogue/scoreLTconf.json").getPath();

        String outputDir = runtimeRessourceFolder.resolve("out").getPath();
        String outputDirVertical = runtimeRessourceFolder.resolve("outVert").getPath();
        String expected = runtimeRessourceFolder.resolve("expected").getPath();

        String[] cmdArgs = {
                inputPathTastebox,
                inputPathCatProfil,
                inputPathCatProfilVector,
                inputPathCatCombiCoeur,
                outputDir,
                outputDirVertical
        };

        MainProfils.main(cmdArgs);

        // Test _Counters
        List<String> expectedCounters = TestUtils.getLinesFromPath(expected, "_COUNTERS");
        List<String> actualCounters = TestUtils.getLinesFromPath(outputDir, "_COUNTERS");
        assertEquals("Counters", expectedCounters, actualCounters);

        // Test optinweights
        List<String> expectedProfils = TestUtils.getLinesFromPath(expected, "part-profils");
        List<String> actualProfils = TestUtils.getLinesFromPath(outputDir+"/profils", "part-*");
        assertEquals("profils", expectedProfils, actualProfils);

        // Test optinweights
        List<String> expectedVectors = TestUtils.getLinesFromPath(expected, "part-vectors");
        List<String> actualVectors = TestUtils.getLinesFromPath(outputDir+"/vectors", "part-*");
        assertEquals("vectors", expectedVectors, actualVectors);

        // Test score vertical

        // 13 aid * 39 thematiques = 507 lines
        List<String> actualScores = TestUtils.getLinesFromPath(outputDirVertical+"/scoresVertical", "part-*");
        assertEquals("count lines", 507, actualScores.size());

        // Filtrer les lignes qui contiennent l'aid spécifié aid = 221877484
        String targetAid = "221877484";
        List<String> filteredScores = actualScores.stream()
                .filter(line -> line.startsWith(targetAid))
                .collect(Collectors.toList());
        assertEquals("count lines for aid 221877484", 39, filteredScores.size());

        // somme des scores for aid 221877484
        double sum = 0.0;
        for (String score : filteredScores) {
            String[] parts = score.split("\t");
            sum += Double.parseDouble(parts[2]);
        }
        assertEquals("Total score = 1", 1, (int) sum);

    }

}
