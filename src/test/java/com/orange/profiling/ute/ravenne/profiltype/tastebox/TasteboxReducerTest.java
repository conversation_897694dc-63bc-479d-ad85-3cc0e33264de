package com.orange.profiling.ute.ravenne.profiltype.tastebox;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.FieldsUtils;

public class TasteboxReducerTest {
    private static final String TAB = FieldsUtils.TAB;

    private URI runtimeRessourceFolder;

    ReduceDriver<Text, Text, Text, Text> reduceDriver;

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();

    @Before
    public void setUp() throws URISyntaxException {
        runtimeRessourceFolder = TasteboxReducerTest.class.getResource("/ute-ravenne-tastebox/").toURI();

        TasteboxReducer reducer = new TasteboxReducer();

        reduceDriver = ReduceDriver.newReduceDriver(reducer);

        String catalogConceptFamily = runtimeRessourceFolder.resolve("in/conceptfamily.csv").getPath();
        reduceDriver.getConfiguration().set(MainTastebox.CONCEPT_FAMILY, catalogConceptFamily);

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public final void testTasteboxReducer() throws IOException, InterruptedException, ClassNotFoundException {

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("thèmes/a=100,type/b=200,actions/c=100"+TAB+"1200"));
        values.add(new Text("thèmes/b=200,actions/a=500,actions/b=200"+TAB+"800"));
        values.add(new Text("thèmes/c=200,unknow/concept=1000,actions/b=100,type/c=100"+TAB+"1600"));
        values.add(new Text("thèmes/d=800,actions/c=100,type/d=300"+TAB+"600"));
        values.add(new Text("type/a=200,actions/d=100,thèmes/b=200"+TAB+"1400"));

        reduceDriver.withInput(new Text("123456789-d4t2"), values);

        reduceDriver.withOutput(new Text("123456789"), new Text(
                "thèmes/d=10,actions/a=6,thèmes/b=5,actions/b=3,type/d=3,type/a=2,type/b=2"+TAB+"d4t2"+TAB+"5600"));

        reduceDriver.withCounter(Counters.CONCEPT_WITHOUT_FAMILY, 1);
        reduceDriver.runTest();
    }

    @Test
    public final void testTasteboxReducerWithOnlyOneConceptForThemeFamily() throws IOException, InterruptedException, ClassNotFoundException {
        // The goals is to test the weightsSize limitation

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("thèmes/d=100,type/b=200,actions/c=100"+TAB+"1200"));
        values.add(new Text("thèmes/d=100,actions/a=500,actions/b=200"+TAB+"800"));
        values.add(new Text("thèmes/d=100,unknow/concept=1000,actions/b=100,type/c=100"+TAB+"1600"));
        values.add(new Text("thèmes/d=800,actions/c=100,type/d=300"+TAB+"600"));
        values.add(new Text("type/a=200,actions/d=100,thèmes/d=100"+TAB+"1400"));

        reduceDriver.withInput(new Text("123456789-d4t2"), values);

        reduceDriver.withOutput(new Text("123456789"), new Text(
                "thèmes/d=10,actions/a=4,actions/b=2,type/d=2"+TAB+"d4t2"+TAB+"5600"));

        reduceDriver.withCounter(Counters.CONCEPT_WITHOUT_FAMILY, 1);
        reduceDriver.runTest();
    }

    @Test
    public final void testTasteboxReducerWithKeptWeight2() throws IOException, InterruptedException, ClassNotFoundException {
        // The goals is to test the PARAM_MINIMUM_WEIGHT_THRESHOLD with theme concepts

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("thèmes/a=100,type/b=200,actions/c=100"+TAB+"1200"));
        values.add(new Text("thèmes/b=100,actions/a=500,actions/b=200"+TAB+"800"));
        values.add(new Text("thèmes/c=100,unknow/concept=1000,actions/b=100,type/c=100"+TAB+"1600"));
        values.add(new Text("thèmes/d=800,actions/c=100,type/d=300"+TAB+"600"));
        values.add(new Text("type/a=200,actions/d=100,thèmes/e=100"+TAB+"1400"));

        reduceDriver.withInput(new Text("123456789-d4t2"), values);

        reduceDriver.withOutput(new Text("123456789"), new Text(
                "thèmes/d=10,actions/a=6,actions/b=3,type/d=3,type/a=2,type/b=2"+TAB+"d4t2"+TAB+"5600"));

        reduceDriver.withCounter(Counters.CONCEPT_WITHOUT_FAMILY, 1);
        reduceDriver.runTest();
    }

    @Test
    public final void testTasteboxReducerWeightZero() throws IOException, InterruptedException, ClassNotFoundException {

        List<Text> values = new ArrayList<Text>();
        values.add(new Text("thèmes/a=0,type/b=0,actions/c=0"+TAB+"500"));
        values.add(new Text("thèmes/b=0,actions/a=0,actions/b=0"+TAB+"700"));
        values.add(new Text("type/a=0,actions/d=0,thèmes/b=0"+TAB+"800"));

        reduceDriver.withInput(new Text("123456789-d4t2"), values);

        // nb of distincts concepts name
        reduceDriver.withCounter(Counters.CONCEPT_WITH_NULL_WEIGHT, 8);
        reduceDriver.runTest();
    }


}
