package com.orange.profiling.ute.ravenne.ponderation;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mrunit.mapreduce.ReduceDriver;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import com.orange.profiling.common.mapred.MosWriter;
import com.orange.profiling.common.utils.FieldsUtils;
import com.orange.profiling.ute.ravenne.dao.TimeboxZapAggregation;

@RunWith(MockitoJUnitRunner.class)
public class PonderationConceptsReducerTest {
    private static final String TAB = FieldsUtils.TAB;
    private static final String AID = "123456789";
    private static final String AID2 = "789456123";
    private static final String AID_HASH = "e5df5f40372ca0df20c2212ac1a27d7abcb54a4245b2e298fc";
    private static final String AID3 = "3571594862";
    private static final String LIVE = "LIVE";
    private static final String VOD = "VOD";

    @Mock
    private MosWriter mockMosWriter;
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    private ReduceDriver<Text, Text, Text, Text> reduceDriver;
    private PonderationConceptsReducer reducer;

    @Before
    public void setUp() {

        reducer = new PonderationConceptsReducer();
        reducer.setMosWriter(mockMosWriter);
        reduceDriver = ReduceDriver.newReduceDriver(reducer);
        reduceDriver.getConfiguration().set(PonderationConceptsReducer.CONF_TOP_CONCEPTS, "20");
        reduceDriver.getConfiguration().set(PonderationConceptsReducer.CONF_TOP_LIVE_CHANNELS, "5");
        reduceDriver.getConfiguration().set(PonderationConceptsReducer.CONF_TOP_OD_PROVIDERS, "5");

        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Before
    public void setUpStreams() {
        System.setErr(new PrintStream(this.errContent));
    }

    @After
    public final void cleanUpStreams() {
        System.setErr(null);
    }

    @Test
    public void testAddConceptsDurationOneValueLive() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String concepts = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String daytimeslot = "d2t6";
        String dayperiod = "d2p3";
        String day = "d2";
        String week = "w";
        String zapDuration = "24";
        String weight = "320";
        String channel = "192";
        String[] zap = { concepts, daytimeslot, LIVE, zapDuration, weight, channel };

        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(daytimeslot, 24L, 0L,
                "root/pa1/pb1/c1=320,root/pa1/pb1/c2=320,root/pa1/pb2/c3=320,root/pa1/pb2=320",
                "192=24", "");

        assertTrue(zapAggregationByTimebox.containsKey(daytimeslot));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(daytimeslot);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(dayperiod));
        actual = zapAggregationByTimebox.get(dayperiod);
        expected = copyWithNewTimebox(dayperiod, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(day));
        actual = zapAggregationByTimebox.get(day);
        expected = copyWithNewTimebox(day, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationTwoValuesLive() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String daytimeslot = "d2t6";
        String dayperiod = "d2p3";
        String day = "d2";
        String week = "w";

        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String zapDuration1 = "24";
        String weight1 = "240";
        String channel1 = "192";
        String[] zap1 = { concepts1, daytimeslot, LIVE, zapDuration1, weight1, channel1 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap1);

        String concepts2 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3";
        String zapDuration2 = "14";
        String weight2 = "120";
        String channel2 = "192";
        String[] zap2 = { concepts2, daytimeslot, LIVE, zapDuration2, weight2, channel2 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap2);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(daytimeslot, 38L, 0L,
                "root/pa1/pb1/c1=360,root/pa1/pb1/c2=240,root/pa1/pb2/c3=360,root/pa1/pb2=360,root/pa1/pb1=120,root/pa1/pb3=120",
                "192=38","");

        assertTrue(zapAggregationByTimebox.containsKey(daytimeslot));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(daytimeslot);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(dayperiod));
        actual = zapAggregationByTimebox.get(dayperiod);
        expected = copyWithNewTimebox(dayperiod, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(day));
        actual = zapAggregationByTimebox.get(day);
        expected = copyWithNewTimebox(day, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationValuesLiveWithYouthProgram() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String daytimeslot = "d2t6";
        String dayperiod = "d2p3";
        String day = "d2";
        String week = "w";

        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String zapDuration1 = "24";
        String weight1 = "240";
        String channel1 = "192";
        String[] zap1 = { concepts1, daytimeslot, LIVE, zapDuration1, weight1, channel1 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap1);

        String concepts2 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3";
        String zapDuration2 = "14";
        String weight2 = "120";
        String channel2 = "192";
        String[] zap2 = { concepts2, daytimeslot, LIVE, zapDuration2, weight2, channel2 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap2);

        String concepts3 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3,audience/pour enfants";
        String zapDuration3 = "14";
        String weight3 = "120";
        String channel3 = "192";
        String[] zap3 = { concepts3, daytimeslot, LIVE, zapDuration3, weight3, channel3 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap3);

        String concepts4 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3,audience/pour les tout-petits";
        String zapDuration4 = "14";
        String weight4 = "120";
        String channel4 = "192";
        String[] zap4 = { concepts4, daytimeslot, LIVE, zapDuration4, weight4, channel4 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap4);

        String concepts5 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3,audience/pour les petits";
        String zapDuration5 = "14";
        String weight5 = "120";
        String channel5 = "192";
        String[] zap5 = { concepts5, daytimeslot, LIVE, zapDuration5, weight5, channel5 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap5);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(daytimeslot, 38L, 0L,
                "root/pa1/pb1/c1=360,root/pa1/pb1/c2=240,root/pa1/pb2/c3=360,root/pa1/pb2=360,root/pa1/pb1=120,root/pa1/pb3=120",
                "192=38","");

        assertTrue(zapAggregationByTimebox.containsKey(daytimeslot));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(daytimeslot);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(dayperiod));
        actual = zapAggregationByTimebox.get(dayperiod);
        expected = copyWithNewTimebox(dayperiod, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(day));
        actual = zapAggregationByTimebox.get(day);
        expected = copyWithNewTimebox(day, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationOneValueVod() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String concepts = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String daytimeslot = "d2t6";
        String day = "d2od";
        String week = "wod";
        String zapDuration = "24";
        String weight = "420";
        String provider = "SVODMFT16";
        String[] zap = { concepts, daytimeslot, VOD, zapDuration, weight, provider };

        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(day, 0L, 24L,
                "root/pa1/pb1/c1=420,root/pa1/pb1/c2=420,root/pa1/pb2/c3=420,root/pa1/pb2=420",
                "", "SVODMFT16=24");

        assertTrue(zapAggregationByTimebox.containsKey(day));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(day);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationValuesVodWithYouthProgram() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String concepts = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String daytimeslot = "d2t6";
        String day = "d2od";
        String week = "wod";
        String zapDuration = "24";
        String weight = "420";
        String provider = "SVODMFT16";
        String[] zap = { concepts, daytimeslot, VOD, zapDuration, weight, provider };

        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap);

        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2,audience/pour enfants";
        String daytimeslot1 = "d2t6";
        String day1 = "d2od";
        String week1 = "wod";
        String zapDuration1 = "24";
        String weight1 = "420";
        String provider1 = "SVODMFT16";
        String[] zap1 = { concepts1, daytimeslot1, VOD, zapDuration1, weight1, provider1 };

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(day, 0L, 24L,
                "root/pa1/pb1/c1=420,root/pa1/pb1/c2=420,root/pa1/pb2/c3=420,root/pa1/pb2=420",
                "", "SVODMFT16=24");

        assertTrue(zapAggregationByTimebox.containsKey(day));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(day);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationTwoValuesVod() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String daytimeslot = "d2t6";
        String day = "d2od";
        String week = "wod";

        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String zapDuration1 = "24";
        String weight1 = "160";
        String provider1 = "SVODMFT16";
        String[] zap1 = { concepts1, daytimeslot, VOD, zapDuration1, weight1, provider1 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap1);

        String concepts2 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3";
        String zapDuration2 = "14";
        String weight2 = "240";
        String provider2 = "VOD2424";
        String[] zap2 = { concepts2, daytimeslot, VOD, zapDuration2, weight2, provider2 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap2);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(day, 0L, 38L,
                "root/pa1/pb1/c1=400,root/pa1/pb1/c2=160,root/pa1/pb2/c3=400,root/pa1/pb2=400,root/pa1/pb1=240,root/pa1/pb3=240",
                "", "SVODMFT16=24,VOD2424=14");

        assertTrue(zapAggregationByTimebox.containsKey(day));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(day);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationChannelTwoValuesLive() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String daytimeslot = "d2t6";
        String dayperiod = "d2p3";
        String day = "d2";
        String week = "w";

        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String zapDuration1 = "24";
        String weight1 = "320";
        String channel1 = "192";
        String[] zap1 = { concepts1, daytimeslot, LIVE, zapDuration1, weight1, channel1 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap1);

        String concepts2 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3";
        String zapDuration2 = "14";
        String weight2 = "180";
        String channel2 = "110";
        String[] zap2 = { concepts2, daytimeslot, LIVE, zapDuration2, weight2, channel2};
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap2);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(daytimeslot, 38L, 0L,
                "root/pa1/pb1/c1=500,root/pa1/pb1/c2=320,root/pa1/pb2/c3=500,root/pa1/pb2=500,root/pa1/pb1=180,root/pa1/pb3=180",
                "192=24,110=14","");

        assertTrue(zapAggregationByTimebox.containsKey(daytimeslot));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(daytimeslot);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(dayperiod));
        actual = zapAggregationByTimebox.get(dayperiod);
        expected = copyWithNewTimebox(dayperiod, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(day));
        actual = zapAggregationByTimebox.get(day);
        expected = copyWithNewTimebox(day, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationTwoValuesLiveVod() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String daytimeslot = "d2t6";
        String dayperiod = "d2p3";
        String day = "d2";
        String week = "w";

        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String zapDuration1 = "24";
        String weight1 = "320";
        String channel1 = "192";
        String[] zap1 = { concepts1, daytimeslot, LIVE, zapDuration1, weight1, channel1 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap1);

        String concepts2 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3";
        String zapDuration2 = "14";
        String weight2 = "180";
        String provider2 = "VOD2424";
        String[] zap2 = { concepts2, daytimeslot, VOD, zapDuration2, weight2, provider2 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap2);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                "root/pa1/pb1/c1=320,root/pa1/pb1/c2=320,root/pa1/pb2=320,root/pa1/pb2/c3=320",
                "192=24","");

        assertTrue(zapAggregationByTimebox.containsKey(daytimeslot));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(daytimeslot);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(dayperiod));
        actual = zapAggregationByTimebox.get(dayperiod);
        expected = copyWithNewTimebox(dayperiod, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(day));
        actual = zapAggregationByTimebox.get(day);
        expected = copyWithNewTimebox(day, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testAddConceptsDurationTwoValuesLiveVodWithYouthProgram() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String daytimeslot = "d2t6";
        String dayperiod = "d2p3";
        String day = "d2";
        String week = "w";

        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String zapDuration1 = "24";
        String weight1 = "320";
        String channel1 = "192";
        String[] zap1 = { concepts1, daytimeslot, LIVE, zapDuration1, weight1, channel1 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap1);

        String concepts2 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3";
        String zapDuration2 = "14";
        String weight2 = "180";
        String provider2 = "VOD2424";
        String[] zap2 = { concepts2, daytimeslot, VOD, zapDuration2, weight2, provider2 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap2);

        String concepts3 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3,audience/pour enfants";
        String zapDuration3 = "14";
        String weight3 = "120";
        String provider3 = "VOD2424";
        String[] zap3 = { concepts3, daytimeslot, VOD, zapDuration3, weight3, provider3 };
        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap3);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                "root/pa1/pb1/c1=320,root/pa1/pb1/c2=320,root/pa1/pb2=320,root/pa1/pb2/c3=320",
                "192=24","");

        assertTrue(zapAggregationByTimebox.containsKey(daytimeslot));
        TimeboxZapAggregation actual = zapAggregationByTimebox.get(daytimeslot);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(dayperiod));
        actual = zapAggregationByTimebox.get(dayperiod);
        expected = copyWithNewTimebox(dayperiod, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(day));
        actual = zapAggregationByTimebox.get(day);
        expected = copyWithNewTimebox(day, expected);
        assertEquals(expected, actual);

        assertTrue(zapAggregationByTimebox.containsKey(week));
        actual = zapAggregationByTimebox.get(week);
        expected = copyWithNewTimebox(week, expected);
        assertEquals(expected, actual);
    }

    @Test
    public void testWriteConceptsWeights() throws IOException, InterruptedException {
        reducer.buildTopSelector(reduceDriver.getConfiguration());
        String aid = "123456";
        String daytimeslot = "d2t5";
        String  weightedConcepts = "root/pa1/pb1=180,root/pa1/pb3=180,root/pa1/pb2=500,"
                +"root/pa1/pb1/c2=320,root/pa1/pb2/c1=500,root/pa1/pb1/c3=500";

        // concepts are reordered by weight
        String expectedWeightedConcepts = "root/pa1/pb1/c3=500,root/pa1/pb2=500,root/pa1/pb2/c1=500,"
                +"root/pa1/pb1/c2=320,root/pa1/pb1=180,root/pa1/pb3=180";

        TimeboxZapAggregation zapAggregation = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                weightedConcepts, "", "");

        reducer.setOutputKey(aid);
        reducer.writeConceptsWeights(daytimeslot, zapAggregation);

        int nbCall = 0;
        nbCall += withConceptsWeights(aid, "dt", daytimeslot, expectedWeightedConcepts, "24", "14");
        Mockito.verify(mockMosWriter, Mockito.times(nbCall))
        .write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    @Test
    public void testLimitConcepts5() throws IOException, InterruptedException {
        reduceDriver.getConfiguration().set(PonderationConceptsReducer.CONF_TOP_CONCEPTS, "5");
        reducer.buildTopSelector(reduceDriver.getConfiguration());
        String aid = "123456";
        String daytimeslot = "d2t5";
        String  weightedConcepts = "root/pa1/pb1=180,root/pa1/pb3=180,root/pa1/pb2=500,"
                +"root/pa1/pb1/c2=320,root/pa1/pb2/c1=500,root/pa1/pb1/c3=500";
        // concepts are reordered by weight and limited to 5 concepts
        String expectedWeightedConcepts = "root/pa1/pb1/c3=500,root/pa1/pb2=500,root/pa1/pb2/c1=500,"
                +"root/pa1/pb1/c2=320,root/pa1/pb1=180";

        TimeboxZapAggregation zapAggregation = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                weightedConcepts, "","");

        reducer.limitConceptNumber(zapAggregation);

        assertEquals(expectedWeightedConcepts, zapAggregation.getWeightedConceptsString());
    }

    @Test
    public void testWriteBestChannels() throws IOException, InterruptedException {
        reducer.buildTopSelector(reduceDriver.getConfiguration());
        String aid = "123456";
        String daytimeslot = "d2t5";
        String liveOrVod = "LIVE";
        String channelsDuration = "900=540,5678=540,123=880,1234=540";
        String expectedChannelsDuration = "123=880,1234=540,5678=540,900=540";

        TimeboxZapAggregation zapAggregation = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                "", channelsDuration,"");

        reducer.setOutputKey(aid);
        reducer.writeBestChannels(daytimeslot, zapAggregation);

        int nbCall = 0;
        nbCall += withOutputProvider(aid, "dt", daytimeslot, expectedChannelsDuration, liveOrVod);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall))
        .write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    @Test
    public void testWriteBestChannelsWithLimit3() throws IOException, InterruptedException {
        reduceDriver.getConfiguration().set(PonderationConceptsReducer.CONF_TOP_LIVE_CHANNELS, "3");
        reducer.buildTopSelector(reduceDriver.getConfiguration());
        String aid = "123456";
        String daytimeslot = "d2t5";
        String liveOrVod = "LIVE";

        String channelsDuration = "900=540,5678=540,123=880,1234=540";
        String expectedChannelsDuration = "123=880,1234=540,5678=540";

        TimeboxZapAggregation zapAggregation = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                "", channelsDuration,"");

        reducer.setOutputKey(aid);
        reducer.writeBestChannels(daytimeslot, zapAggregation);

        int nbCall = 0;
        nbCall += withOutputProvider(aid, "dt", daytimeslot, expectedChannelsDuration, liveOrVod);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall))
        .write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    @Test
    public void testWriteBestProviders() throws IOException, InterruptedException {
        reducer.buildTopSelector(reduceDriver.getConfiguration());
        String aid = "123456";
        String daytimeslot = "d2od";
        String liveOrVod = "VOD";
        String providersDuration = "catchuptv_xxx=5400";
        String expectedProvidersDuration = "catchuptv_xxx=5400";

        TimeboxZapAggregation zapAggregation = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                "", "", providersDuration);

        reducer.setOutputKey(aid);
        reducer.writeBestProviders(daytimeslot, zapAggregation);

        int nbCall = 0;
        nbCall += withOutputProvider(aid, "dod", daytimeslot, expectedProvidersDuration, liveOrVod);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall))
        .write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    @Test
    public void testWriteBestProvidersWithLimit3() throws IOException, InterruptedException {
        reduceDriver.getConfiguration().set(PonderationConceptsReducer.CONF_TOP_OD_PROVIDERS, "3");
        reducer.buildTopSelector(reduceDriver.getConfiguration());
        String aid = "123456";
        String daytimeslot = "d2t5";
        String liveOrVod = "VOD";

        String providersDuration = "catchuptv_1=5400,catchuptv_2=68800,catchuptv_3=31540,catchuptv_4=5400";
        String expectedProvidersDuration = "catchuptv_2=68800,catchuptv_3=31540,catchuptv_1=5400";

        TimeboxZapAggregation zapAggregation = buildTimeboxZapAggregation(daytimeslot, 24L, 14L,
                "", "", providersDuration);

        reducer.setOutputKey(aid);
        reducer.writeBestProviders(daytimeslot, zapAggregation);

        int nbCall = 0;
        nbCall += withOutputProvider(aid, "dt", daytimeslot, expectedProvidersDuration, liveOrVod);
        Mockito.verify(mockMosWriter, Mockito.times(nbCall))
        .write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    @Test
    public void testContainsConceptsJeunesse() {
        String concepts1 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3";
        String concepts2 = "root/pa1/pb1/c1,root/pa1/pb1,root/pa1/pb2/c3,root/pa1/pb2,root/pa1/pb3,audience/pour enfants";

        assertTrue(PonderationConceptsReducer.containsConceptsJeunesse(concepts2));
        assertFalse(PonderationConceptsReducer.containsConceptsJeunesse(concepts1));
    }

    private static TimeboxZapAggregation buildTimeboxZapAggregation(
            String timebox, Long liveDuration, Long vodDuration,
            String weightedConcepts,
            String liveChannelDuration, String odProviderDuration) {

        TimeboxZapAggregation tbcd = new TimeboxZapAggregation(timebox);
        tbcd.addDuration(LIVE, liveDuration);
        tbcd.addDuration(VOD, vodDuration);
        tbcd.setWeightedConcepts(weightedStringToMap(weightedConcepts));
        tbcd.setLiveChannelDuration(weightedStringToMap(liveChannelDuration));
        tbcd.setOdProviderDuration(weightedStringToMap(odProviderDuration));
        return tbcd;
    }

    private static Map<String, Long> weightedStringToMap(String weightedString) {
        return Arrays.stream(FieldsUtils.COMMA_PATTERN.split(weightedString))
                .filter(e -> (e!=null && !e.isEmpty()))
                .map(e -> FieldsUtils.EQUAL_PATTERN.split(e))
                .collect(Collectors.toMap(e -> e[0], e -> Long.parseLong(e[1])));
    }

    private static TimeboxZapAggregation copyWithNewTimebox(
            String timebox, TimeboxZapAggregation info) {

        TimeboxZapAggregation tbcd = new TimeboxZapAggregation(timebox);
        tbcd.addDuration(LIVE, info.getLiveDuration());
        tbcd.addDuration(VOD, info.getVodDuration());
        tbcd.setWeightedConcepts(info.getWeightedConcepts());
        tbcd.setLiveChannelDuration(info.getLiveChannelDuration());
        tbcd.setOdProviderDuration(info.getOdProviderDuration());
        return tbcd;
    }

    @Test
    public void testReduceOptinTvIs1() throws IOException, InterruptedException {

        String conceptsList;
        List<Text> values = new ArrayList<Text>();
        conceptsList = "catégories/fiction/série,lieu de l'action/Europe/Europe de l'ouest/France,type/film choral,thèmes/amour impossible,ambiance et ton/réaliste";
        values.add(new Text(String.join(TAB, conceptsList, "d4t5", LIVE, "36", "360", "123", LIVE, "4")));
        conceptsList = "catégories/fiction/série,lieu de l'action/Europe/Europe de l'ouest/Allemagne,type/film choral,thèmes/mer,ambiance et ton/réaliste";
        values.add(new Text(String.join(TAB, conceptsList, "d4t6", LIVE, "24", "240", "132", LIVE, "4")));
        conceptsList = "catégories/fiction/série,lieu de l'action/Europe/Europe de l'ouest/Allemagne,type/film choral,thèmes/amour impossible";
        values.add(new Text(String.join(TAB,conceptsList, "d4t5", VOD, "18", "180", "VOD1|catchup2", "VOD", "4")));
        conceptsList = "catégories/fiction/série,lieu de l'action/Europe/Europe de l'ouest/France,type/film choral,thèmes/mer";
        values.add(new Text(String.join(TAB,conceptsList, "d4t6", VOD, "12", "120", "VOD1|catchup2", "VOD", "4")));
        conceptsList = "catégories/fiction/série,lieu de l'action/Europe/Europe de l'ouest/France,thèmes/mer";
        values.add(new Text(String.join(TAB,conceptsList, "d4t6", VOD, "8", "80", "REPLAY1|catchup1", "TVOD", "4")));

        String optinCalculation = "OPTIN\t"+AID+"\t1\tD0:3E:96:ED:10:10\tWHD94" +
                "\te5df5f40372ca0df20c2212ac1a27d7abcb54a4245b2e298fc" +
                "\t0028df042b84e42a97dc2fcf6524ffd22600f592ae08ff8ba" +
                "\t1\t0\t1566241608\tCamela16db000";
        values.add(new Text(optinCalculation));

        reduceDriver.withInput(new Text(AID), values);

        reduceDriver.withCounter(Counters.WEIGHTED_AID, 1);

        //d4  w  d4od  d4t6  d4p3  wod  d4t5
        reduceDriver.withCounter(Counters.NB_GENETIC_PROFILES, 11);
        reduceDriver.withCounter(Counters.NB_CONCEPTS_TOTAL, 99);
        reduceDriver.withCounter(Counters.NB_CATCHUP_CHANNEL_ID, 3);

        reduceDriver.withCounter(PonderationConceptsReducer.NB_PROGRAM_COUNTER_GROUP,
                PonderationConceptsReducer.NB_PROGRAM_COUNTER_GROUP
                + PonderationConceptsReducer.NB_PROGRAM_LESS_THAN_FIVE, 2);
        reduceDriver.withCounter(PonderationConceptsReducer.NB_PROGRAM_COUNTER_TIMESLOT,
                PonderationConceptsReducer.NB_PROGRAM_COUNTER_TIMESLOT+"05"
                        + PonderationConceptsReducer.NB_PROGRAM_LESS_THAN_FIVE, 1);
        reduceDriver.withCounter(PonderationConceptsReducer.NB_PROGRAM_COUNTER_TIMESLOT,
                PonderationConceptsReducer.NB_PROGRAM_COUNTER_TIMESLOT+"06"
                        + PonderationConceptsReducer.NB_PROGRAM_LESS_THAN_FIVE, 1);

        reduceDriver.runTest();
        Mockito.verify(mockMosWriter).open(Mockito.any());


        int nbCall = 0;
        nbCall += withNbProgram(AID_HASH, "d4t6", "3");
        nbCall += withNbProgram(AID_HASH, "d4t5", "2");

        String expectedWeightedConcepts;
        String expectedChannelsDuration;
        String expectedProvidersDuration;



        expectedWeightedConcepts = "ambiance et ton/réaliste=600,type/film choral=600," +
                "thèmes/amour impossible=360,catégories/fiction=300,catégories/série=300," +
                "lieu de l'action/France=270,thèmes/mer=240,lieu de l'action/Allemagne=180," +
                "lieu de l'action/Europe=75,lieu de l'action/Europe de l'ouest=75";
        nbCall += withConceptsWeights(AID_HASH, "d", "d4", expectedWeightedConcepts, "60", "38");


        expectedWeightedConcepts = "ambiance et ton/réaliste=360,thèmes/amour impossible=360,type/film choral=360,catégories/fiction=180," +
                "catégories/série=180,lieu de l'action/France=180,lieu de l'action/Europe=90,lieu de l'action/Europe de l'ouest=90";
        nbCall += withConceptsWeights(AID_HASH, "dt", "d4t5", expectedWeightedConcepts, "36", "18");


        expectedWeightedConcepts = "ambiance et ton/réaliste=600,type/film choral=600,thèmes/amour impossible=360,catégories/fiction=300," +
                "catégories/série=300,lieu de l'action/France=270,thèmes/mer=240,lieu de l'action/Allemagne=180," +
                "lieu de l'action/Europe=75,lieu de l'action/Europe de l'ouest=75";
        nbCall += withConceptsWeights(AID_HASH, "dp", "d4p3", expectedWeightedConcepts, "60", "38");

        nbCall += withConceptsWeights(AID_HASH, "d", "d4", expectedWeightedConcepts, "60", "38");
        nbCall += withConceptsWeights(AID_HASH, "w", "w", expectedWeightedConcepts, "60", "38");


        expectedChannelsDuration = "123=36";
        nbCall += withOutputProvider(AID_HASH, "dt", "d4t5", expectedChannelsDuration, "LIVE");
        expectedChannelsDuration = "132=24";
        nbCall += withOutputProvider(AID_HASH, "dt", "d4t6", expectedChannelsDuration, "LIVE");
        expectedChannelsDuration = "123=36,132=24";
        nbCall += withOutputProvider(AID_HASH, "dp", "d4p3", expectedChannelsDuration, "LIVE");
        nbCall += withOutputProvider(AID_HASH, "d", "d4", expectedChannelsDuration, "LIVE");
        nbCall += withOutputProvider(AID_HASH, "w", "w", expectedChannelsDuration, "LIVE");

        expectedProvidersDuration = "catchup2=30,catchup1=8";
        nbCall += withOutputProvider(AID_HASH, "dod", "d4od", expectedProvidersDuration, "VOD");
        nbCall += withOutputProvider(AID_HASH, "wod", "wod", expectedProvidersDuration, "VOD");

        //Mockito.verify(mockMosWriter, Mockito.times(nbCall)).write(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());


        Mockito.verify(mockMosWriter).close();
    }

    @Test
    public void testReduceOptinTvIs0() throws IOException, InterruptedException {
        String conceptsList;
        List<Text> values = new ArrayList<Text>();
        conceptsList = "catégories/fiction/série,lieu de l'action/Europe/Europe de l'ouest/France,type/film choral,thèmes/amour impossible,ambiance et ton/réaliste";
        values.add(new Text(String.join(TAB, conceptsList, "d4t5", LIVE, "36", "360", "123", LIVE, "4")));

        String optinCalculation = "OPTIN\t"+AID+"\t1\tD0:3E:96:ED:10:10\tWHD94" +
                "\te5df5f40372ca0df20c2212ac1a27d7abcb54a4245b2e298fc" +
                "\t0028df042b84e42a97dc2fcf6524ffd22600f592ae08ff8ba" +
                "\t0\t1\t1566241608\tCamela16db000";
        values.add(new Text(optinCalculation));

        reduceDriver.withInput(new Text(AID), values);

        reduceDriver.withCounter(Counters.WEIGHTED_AID, 0);

        reduceDriver.runTest();
    }

    @Test
    public void testReduceFoyerReplay() throws IOException {
        String conceptsList;
        List<Text> valuesAID1 = new ArrayList<Text>();
        List<Text> valuesAID2 = new ArrayList<Text>();
        List<Text> valuesAID3 = new ArrayList<Text>();
        conceptsList = "catégories/fiction/série,lieu de l'action/Europe/Europe de l'ouest/France,type/film choral,thèmes/amour impossible,ambiance et ton/réaliste";
        valuesAID1.add(new Text(String.join(TAB, conceptsList, "d4t5", LIVE, "36", "360", "123", LIVE, "4")));
        valuesAID1.add(new Text(String.join(TAB,conceptsList, "d4t6", VOD, "18", "80", "REPLAY1|catchup1", "TVOD", "4")));
        valuesAID1.add(new Text(String.join(TAB,conceptsList, "d1t2", VOD, "41", "40", "REPLAY1|catchup1", "TVOD", "1")));
        valuesAID1.add(new Text(String.join(TAB,conceptsList, "d2t4", VOD, "86", "80", "REPLAY1|catchup1", "TVOD", "2")));
        valuesAID1.add(new Text(String.join(TAB,conceptsList, "d4t6", VOD, "12", "20", "VOD1|catchup2", "VOD", "4")));
        valuesAID1.add(new Text(String.join(TAB,conceptsList, "d5t6", VOD, "16", "60", "REPLAY1|catchup1", "TVOD", "5")));
        valuesAID1.add(new Text(String.join(TAB,conceptsList, "d5t6", VOD, "16", "60", "REPLAY2|catchup2", "TVOD", "5")));
        valuesAID1.add(new Text(String.join(TAB,conceptsList, "d7t6", VOD, "216", "460", "REPLAY1|catchup1", "TVOD", "7")));

        valuesAID2.add(new Text(String.join(TAB, conceptsList, "d4t5", LIVE, "36", "360", "123", LIVE, "4")));
        valuesAID2.add(new Text(String.join(TAB,conceptsList, "d3t6", VOD, "16", "60", "REPLAY1|catchup1", "TVOD", "3")));
        valuesAID2.add(new Text(String.join(TAB,conceptsList, "d5t6", VOD, "16", "60", "REPLAY1|catchup1", "TVOD", "5")));

        valuesAID3.add(new Text(String.join(TAB, conceptsList, "d1t5", LIVE, "36", "360", "123", LIVE, "1")));
        valuesAID3.add(new Text(String.join(TAB,conceptsList, "d4t6", VOD, "12", "120", "VOD1|catchup2", "VOD", "4")));
        valuesAID3.add(new Text(String.join(TAB,conceptsList, "d5t6", VOD, "19", "130", "REPLAY1|catchup2", "TVOD", "5")));
        valuesAID3.add(new Text(String.join(TAB,conceptsList, "d6t6", VOD, "35", "660", "REPLAY1|catchup1", "TVOD", "6")));
        valuesAID3.add(new Text(String.join(TAB,conceptsList, "d9t6", VOD, "354", "60", "REPLAY1|catchup1", "TVOD", "9")));
        valuesAID3.add(new Text(String.join(TAB,conceptsList, "d7t6", VOD, "216", "460", "REPLAY1|catchup1", "TVOD", "7")));

        String optinCalculationAID1 = "OPTIN\t"+AID+"\t1\tD0:3E:96:ED:10:10\tWHD94" +
                "\te5df5f40372ca0df20c2212ac1a27d7abcb54a4245b2e298fc" +
                "\t0028df042b84e42a97dc2fcf6524ffd22600f592ae08ff8ba" +
                "\t1\t0\t1566241608\tCamela16db000";

        String optinCalculationAID2 = "OPTIN\t"+AID2+"\t1\tD0:3E:96:ED:10:10\tWHD94" +
                "\te5df5f40372ca0df20c2212ac1a27d7abcb54a4245b2e298fc" +
                "\t0028df042b84e42a97dc2fcf6524ffd22600f592ae08ff8ba" +
                "\t1\t0\t1566241608\tCamela16db000";

        String optinCalculationAID3 = "OPTIN\t"+AID3+"\t1\tD0:3E:96:ED:10:10\tWHD94" +
                "\te5df5f40372ca0df20c2212ac1a27d7abcb54a4245b2e298fc" +
                "\t0028df042b84e42a97dc2fcf6524ffd22600f592ae08ff8ba" +
                "\t1\t0\t1566241608\tCamela16db000";

        valuesAID1.add(new Text(optinCalculationAID1));
        valuesAID2.add(new Text(optinCalculationAID2));
        valuesAID3.add(new Text(optinCalculationAID3));


        reduceDriver.withInput(new Text(AID), valuesAID1);
        reduceDriver.withInput(new Text(AID2), valuesAID2);
        reduceDriver.withInput(new Text(AID3), valuesAID3);

        reduceDriver.withCounter(Counters.NB_FOYER_REPLAY_24H_COUNTER, 1);

        reduceDriver.withCounter(Counters.NB_FOYER_REPLAY_4DAYS_PER_7_COUNTER, 1);

        reduceDriver.runTest();
    }


    @Test
    public void testGetCategoryNbProgramLessThanFive() {
        String category = PonderationConceptsReducer.getCategoryNbProgram(4);
        assertEquals(PonderationConceptsReducer.NB_PROGRAM_LESS_THAN_FIVE, category);
    }

    @Test
    public void testGetCategoryNbProgramFive() {
        String category = PonderationConceptsReducer.getCategoryNbProgram(5);
        assertEquals(PonderationConceptsReducer.NB_PROGRAM_FIVE_TO_TEN, category);
    }

    @Test
    public void testGetCategoryNbProgramTen() {
        String category = PonderationConceptsReducer.getCategoryNbProgram(10);
        assertEquals(PonderationConceptsReducer.NB_PROGRAM_FIVE_TO_TEN, category);
    }

    @Test
    public void testGetCategoryNbProgramMoreThanTen() {
        String category = PonderationConceptsReducer.getCategoryNbProgram(11);
        assertEquals(PonderationConceptsReducer.NB_PROGRAM_MORE_THAN_TEN, category);
    }

    private int withNbProgram(String aidHash, String timeslot, String nbProgram)
            throws IOException, InterruptedException {
        String output = timeslot + TAB + nbProgram;
        Mockito.verify(mockMosWriter).write("nbprogram",aidHash, output, "nbprogram/part");
        return 1;
    }

    private int withConceptsWeights(String aid, String timeboxtype, String timebox, String expectedWeightedConcepts, String liveDuration, String vodDuration) throws IOException, InterruptedException {
        String output = String.join(TAB,timebox, expectedWeightedConcepts, liveDuration, vodDuration);
        Mockito.verify(mockMosWriter).write("conceptsweights",aid, output, "conceptsweights/"+timeboxtype);
        return 1;
    }

    private int withOutputProvider(String aid, String timeboxtype, String timebox,
            String expectedProviderDuration, String liveOrVod) throws IOException, InterruptedException {

        String expectedValue = String.join(TAB, timebox, liveOrVod, expectedProviderDuration);
        String part = "/part";
        Mockito.verify(mockMosWriter).write(MainPonderationConcepts.OUT_BEST_CHANNELS_PROVIDERS, aid, expectedValue,
                MainPonderationConcepts.OUT_BEST_CHANNELS_PROVIDERS+Path.SEPARATOR+liveOrVod
                +Path.SEPARATOR+timeboxtype);
        return 1;
    }

    @Test
    public void testAggregateZapShouldContain_d2p1od() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String concepts = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String daytimeslot = "d2t3"; // timeslotIndex 7 is associated to periodIndex 4. dxpyod is returned only for periodIndex between 1 and 3
        String day = "d2od";
        String week = "wod";
        String zapDuration = "24";
        String weight = "420";
        String provider = "SVODMFT16";
        String[] zap = { concepts, daytimeslot, VOD, zapDuration, weight, provider };

        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(day, 0L, 24L,
                "root/pa1/pb1/c1=420,root/pa1/pb1/c2=420,root/pa1/pb2/c3=420,root/pa1/pb2=420",
                "", "SVODMFT16=24");

        assertTrue("The key 'd2p1od' must exist in the HashMap.", zapAggregationByTimebox.containsKey("d2p1od"));
    }

    @Test
    public void testAggregateZapdShouldContain_d4p2od() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String concepts = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String daytimeslot = "d4t5"; // timeslotIndex 5 is associated to periodIndex 3. dxpyod is returned only for periodIndex between 1 and 3
        String day = "d4od";
        String week = "wod";
        String zapDuration = "24";
        String weight = "420";
        String provider = "SVODMFT16";
        String[] zap = { concepts, daytimeslot, VOD, zapDuration, weight, provider };

        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(day, 0L, 24L,
                "root/pa1/pb1/c1=420,root/pa1/pb1/c2=420,root/pa1/pb2/c3=420,root/pa1/pb2=420",
                "", "SVODMFT16=24");

        assertTrue("The key 'd4p2od' must exist in the HashMap.", zapAggregationByTimebox.containsKey("d4p2od"));
    }

    @Test
    public void testAggregateZapdShouldContain_d5p3od() {
        Map<String, TimeboxZapAggregation> zapAggregationByTimebox = new HashMap<>();

        String concepts = "root/pa1/pb1/c1,root/pa1/pb1/c2,root/pa1/pb2/c3,root/pa1/pb2";
        String daytimeslot = "d5t8"; // timeslotIndex 5 is associated to periodIndex 3. dxpyod is returned only for periodIndex between 1 and 3
        String day = "d5od";
        String week = "wod";
        String zapDuration = "24";
        String weight = "420";
        String provider = "SVODMFT16";
        String[] zap = { concepts, daytimeslot, VOD, zapDuration, weight, provider };

        PonderationConceptsReducer.aggregateZap(zapAggregationByTimebox, zap);

        TimeboxZapAggregation expected = buildTimeboxZapAggregation(day, 0L, 24L,
                "root/pa1/pb1/c1=420,root/pa1/pb1/c2=420,root/pa1/pb2/c3=420,root/pa1/pb2=420",
                "", "SVODMFT16=24");

        assertTrue("The key 'd5p3od' must exist in the HashMap.", zapAggregationByTimebox.containsKey("d5p3od"));
    }
}
