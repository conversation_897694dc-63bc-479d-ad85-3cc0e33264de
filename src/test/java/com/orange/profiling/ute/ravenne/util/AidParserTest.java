import static org.junit.Assert.*;

import com.orange.profiling.ute.ravenne.util.AidParser;
import org.junit.Test;

public class AidParserTest {
    
    @Test
    public void testStbFormat() {
        AidParser parser = new AidParser("ABC123-STB");
        assertTrue(parser.isStb());
        assertFalse(parser.isOtt());
        assertEquals("ABC123", parser.getBaseAid());
        assertNull(parser.getDomain());
    }

    @Test
    public void testOttWebFormat() {
        AidParser parser = new AidParser("ABC123-Web");
        assertFalse(parser.isStb());
        assertTrue(parser.isOtt());
        assertEquals("ABC123", parser.getBaseAid());
        assertEquals("Web", parser.getDomain());
    }

    @Test
    public void testOttMobileFormat() {
        AidParser parser = new AidParser("ABC123-Mobile");
        assertTrue(parser.isOtt());
        assertEquals("ABC123", parser.getBaseAid());
        assertEquals("Mobile", parser.getDomain());
    }

    @Test
    public void testOttSmartTVFormat() {
        AidParser parser = new AidParser("ABC123-SmartTV");
        assertTrue(parser.isOtt());
        assertEquals("ABC123", parser.getBaseAid());
        assertEquals("SmartTV", parser.getDomain());
    }

    @Test
    public void testInvalidFormat() {
        AidParser parser = new AidParser("ABC123-Invalid");
        assertFalse(parser.isValidFormat());
        assertEquals("ABC123", parser.getBaseAid());
        assertNull(parser.getDomain());
    }
}