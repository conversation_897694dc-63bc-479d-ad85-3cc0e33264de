out	NB_FOYERS_HAVING_1_PROFILES	9	NB_FOYERS_HAVING_PROFILE
out	NB_FOYERS_HAVING_2_PROFILES	2	NB_FOYERS_HAVING_PROFILE
out	NB_FOYERS_HAVING_3_PROFILES	1	NB_FOYERS_HAVING_PROFILE
out	NB_FOYERS_HAVING_5_PROFILES	1	NB_FOYERS_HAVING_PROFILE
out	NB_PROFILES_FOYERS10	3	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS21	1	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS23	1	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS27	2	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS30	5	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS35	2	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS4	4	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS7	1	NB_PROFILES_FOYERS
out	NB_PROFILES_FOYERS9	2	NB_PROFILES_FOYERS
out	NB_VECTOR_BY_PROFIL10	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL21	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL23	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL27	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL30	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL35	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL4	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL5	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL7	1	NB_VECTOR_BY_PROFIL
out	NB_VECTOR_BY_PROFIL9	1	NB_VECTOR_BY_PROFIL
out	NB_AID_ALGO_TOPCONCEPTS	6	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
out	NB_AID_ALGO_VECTORTYPE	7	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
out	NB_PROFIL	25	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
out	NB_PROFIL_ALGO_TOPCONCEPTS	7	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
out	NB_PROFIL_ALGO_VECTORTYPE	18	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
out	NB_USER_WITH_PROFIL	13	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
out	NB_VECTOR	10	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
out	NO_PROFIL_FOUND	5	com.orange.profiling.ute.ravenne.profiltype.profils.Counters
